"""
根据2024年最新CSDN文章更新的API测试
"""
import requests
import json

def test_new_api_format(stock_code):
    """使用最新的API格式测试"""
    url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': 'www.cninfo.com.cn',
        'Origin': 'http://www.cninfo.com.cn',
        'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice'
    }
    
    # 根据最新文章的参数格式
    data = {
        "pageNum": "1",
        "pageSize": "30", 
        "column": "szse",
        "tabName": "fulltext",
        "plate": "szcy",  # 创业板
        "stock": stock_code,
        "searchkey": "年度报告",
        "secid": "",
        "category": "category_ndbg_szsh",
        "trade": "",
        "seDate": "2022-01-01~2024-12-31",
        "sortName": "code",
        "sortType": "desc",
        "isHLtitle": "true"
    }
    
    try:
        print(f"测试股票 {stock_code} (新API格式):")
        response = requests.post(url, headers=headers, data=data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"返回数据键: {list(result.keys())}")
            
            if 'announcements' in result and result['announcements']:
                announcements = result['announcements']
                print(f"找到 {len(announcements)} 条公告")
                
                # 查找年报
                for i, announcement in enumerate(announcements[:5]):
                    title = announcement.get('announcementTitle', 'N/A')
                    time = announcement.get('announcementTime', 'N/A')
                    print(f"  {i+1}. {title}")
                    print(f"     时间: {time}")
                    
            else:
                print("没有找到公告数据")
                print(f"totalRecordNum: {result.get('totalRecordNum', 'N/A')}")
                print(f"完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("-" * 50)

def test_without_searchkey(stock_code):
    """不使用searchkey的测试"""
    url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': 'www.cninfo.com.cn',
        'Origin': 'http://www.cninfo.com.cn',
        'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice'
    }
    
    # 不使用searchkey，看看能获取到什么
    data = {
        "pageNum": "1",
        "pageSize": "30", 
        "column": "szse",
        "tabName": "fulltext",
        "plate": "szcy",  # 创业板
        "stock": stock_code,
        "searchkey": "",  # 空的searchkey
        "secid": "",
        "category": "category_ndbg_szsh",
        "trade": "",
        "seDate": "2022-01-01~2024-12-31",
        "sortName": "code",
        "sortType": "desc",
        "isHLtitle": "true"
    }
    
    try:
        print(f"测试股票 {stock_code} (不使用searchkey):")
        response = requests.post(url, headers=headers, data=data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'announcements' in result and result['announcements']:
                announcements = result['announcements']
                print(f"找到 {len(announcements)} 条公告")
                
                # 查找年报相关
                annual_reports = []
                for announcement in announcements:
                    title = announcement.get('announcementTitle', '')
                    if '年度报告' in title or '年报' in title:
                        annual_reports.append(announcement)
                
                print(f"其中年报相关: {len(annual_reports)} 条")
                for i, report in enumerate(annual_reports[:3]):
                    print(f"  {i+1}. {report.get('announcementTitle', 'N/A')}")
                    print(f"     时间: {report.get('announcementTime', 'N/A')}")
                    
            else:
                print("没有找到公告数据")
                print(f"totalRecordNum: {result.get('totalRecordNum', 'N/A')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("-" * 50)

if __name__ == "__main__":
    # 测试您的股票代码
    test_stock = '300454'
    
    test_new_api_format(test_stock)
    test_without_searchkey(test_stock)
