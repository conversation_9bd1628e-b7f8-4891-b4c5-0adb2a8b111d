"""
测试刷新数据功能的修复
"""
import os
import pandas as pd
import time
from datetime import datetime

def create_test_excel_files():
    """创建测试Excel文件来验证刷新功能"""
    
    # 确保目录存在
    os.makedirs("results/excel", exist_ok=True)
    
    print("🧪 创建测试Excel文件...")
    
    # 创建第一个测试文件（旧数据）
    old_data = {
        '文件名': [
            '300454_深信服_2024年年度报告',
            '300504_天邑股份_2024年年度报告'
        ],
        '协同创新': [10, 5],
        '合作开发': [15, 8],
        '战略合作': [20, 12]
    }
    
    df_old = pd.DataFrame(old_data)
    old_file = "results/excel/company_single_pdf.xlsx"
    
    with pd.ExcelWriter(old_file, engine='openpyxl') as writer:
        df_old.to_excel(writer, sheet_name='统计结果', index=False)
    
    print(f"✅ 创建旧数据文件: {old_file}")
    print(f"   数据: {len(df_old)} 行, {len(df_old.columns)-1} 个关键词")
    
    # 等待一秒确保时间戳不同
    time.sleep(1)
    
    return old_file

def update_test_excel_file():
    """更新测试Excel文件（模拟关键词分析后的新数据）"""
    
    print("\n🔄 更新Excel文件（模拟关键词分析）...")
    
    # 创建新的测试数据（更多关键词和数据）
    new_data = {
        '文件名': [
            '300454_深信服_2024年年度报告',
            '300504_天邑股份_2024年年度报告',
            '300514_友讯达_2024年年度报告'
        ],
        '协同创新': [15, 8, 12],
        '合资设立': [3, 5, 2],
        '合作开发': [22, 18, 15],
        '合作研发': [18, 12, 9],
        '共同开发': [7, 4, 6],
        '联合研发': [12, 9, 8],
        '战略合作': [25, 20, 18],
        '整合资源': [8, 6, 5]
    }
    
    df_new = pd.DataFrame(new_data)
    new_file = "results/excel/company_single_pdf.xlsx"
    
    with pd.ExcelWriter(new_file, engine='openpyxl') as writer:
        df_new.to_excel(writer, sheet_name='统计结果', index=False)
    
    print(f"✅ 更新数据文件: {new_file}")
    print(f"   新数据: {len(df_new)} 行, {len(df_new.columns)-1} 个关键词")
    
    return new_file

def test_file_detection():
    """测试文件检测逻辑"""
    
    print("\n🔍 测试文件检测逻辑...")
    
    # 模拟update_excel_file_path方法的逻辑
    excel_file_xlsx = os.path.join(os.getcwd(), "results", "excel", "company_single_pdf.xlsx")
    excel_file_xls = os.path.join(os.getcwd(), "results", "excel", "company_single_pdf.xls")
    
    xlsx_exists = os.path.exists(excel_file_xlsx)
    xls_exists = os.path.exists(excel_file_xls)
    
    print(f"📁 XLSX文件存在: {xlsx_exists}")
    print(f"📁 XLS文件存在: {xls_exists}")
    
    if xlsx_exists:
        xlsx_time = os.path.getmtime(excel_file_xlsx)
        xlsx_time_str = datetime.fromtimestamp(xlsx_time).strftime("%Y-%m-%d %H:%M:%S")
        print(f"📅 XLSX修改时间: {xlsx_time_str}")
    
    if xls_exists:
        xls_time = os.path.getmtime(excel_file_xls)
        xls_time_str = datetime.fromtimestamp(xls_time).strftime("%Y-%m-%d %H:%M:%S")
        print(f"📅 XLS修改时间: {xls_time_str}")
    
    # 确定应该使用的文件
    if xlsx_exists and xls_exists:
        xlsx_time = os.path.getmtime(excel_file_xlsx)
        xls_time = os.path.getmtime(excel_file_xls)
        selected_file = excel_file_xlsx if xlsx_time >= xls_time else excel_file_xls
        print(f"✅ 选择文件: {os.path.basename(selected_file)} (较新的文件)")
    elif xlsx_exists:
        selected_file = excel_file_xlsx
        print(f"✅ 选择文件: {os.path.basename(selected_file)} (仅XLSX存在)")
    elif xls_exists:
        selected_file = excel_file_xls
        print(f"✅ 选择文件: {os.path.basename(selected_file)} (仅XLS存在)")
    else:
        print("❌ 没有找到Excel文件")
        return None
    
    return selected_file

def verify_data_content(file_path):
    """验证数据内容"""
    
    if not file_path or not os.path.exists(file_path):
        print("❌ 文件不存在，无法验证内容")
        return
    
    print(f"\n📊 验证文件内容: {os.path.basename(file_path)}")
    
    try:
        df = pd.read_excel(file_path)
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        print(f"   关键词数: {len(df.columns) - 1}")
        
        # 显示列名
        print(f"   列名: {list(df.columns)}")
        
        # 显示数据摘要
        if len(df) > 0:
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                total_count = df[numeric_cols].sum().sum()
                print(f"   关键词总计: {total_count} 次")
        
        print("✅ 数据验证完成")
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")

def main():
    """主测试函数"""
    
    print("🧪 测试刷新数据功能修复")
    print("="*50)
    
    # 1. 创建初始测试文件
    old_file = create_test_excel_files()
    
    # 2. 测试初始文件检测
    selected_file = test_file_detection()
    verify_data_content(selected_file)
    
    # 3. 模拟用户操作：等待几秒后更新文件
    print(f"\n⏳ 等待3秒后模拟关键词分析...")
    time.sleep(3)
    
    # 4. 更新文件（模拟关键词分析生成新数据）
    new_file = update_test_excel_file()
    
    # 5. 再次测试文件检测（应该检测到新文件）
    selected_file = test_file_detection()
    verify_data_content(selected_file)
    
    print(f"\n🎯 测试总结:")
    print(f"✅ 文件检测逻辑正常")
    print(f"✅ 时间戳比较正常")
    print(f"✅ 数据内容验证正常")
    print(f"\n💡 现在数据展示窗口的刷新功能应该能正确检测到最新数据")
    print(f"📋 测试步骤:")
    print(f"   1. 启动GUI程序")
    print(f"   2. 点击'关键词分析'生成新数据")
    print(f"   3. 点击'查看结果'打开数据窗口")
    print(f"   4. 修改关键词后再次点击'关键词分析'")
    print(f"   5. 在数据窗口中点击'刷新数据'")
    print(f"   6. 验证数据是否更新")

if __name__ == "__main__":
    main()
