"""
测试文件组织功能
"""
import os
import pandas as pd
from datetime import datetime

def create_test_structure():
    """创建测试的文件结构"""
    
    # 创建基础目录
    base_dirs = ['results', 'results/pdf', 'results/txt', 'results/excel', 'results/历史记录']
    for dir_name in base_dirs:
        os.makedirs(dir_name, exist_ok=True)
    
    # 创建测试数据
    test_data_single = {
        '文件名': [
            '300454_深信服_2024年年度报告',
            '300454_深信服_2023年年度报告'
        ],
        '协同创新': [15, 10],
        '合作开发': [22, 20],
        '战略合作': [25, 22]
    }
    
    test_data_multiple = {
        '文件名': [
            '300454_深信服_2024年年度报告',
            '300504_天邑股份_2024年年度报告',
            '300514_友讯达_2024年年度报告'
        ],
        '协同创新': [15, 8, 12],
        '合作开发': [22, 18, 15],
        '战略合作': [25, 20, 18]
    }
    
    # 模拟单个公司的结果
    timestamp1 = datetime.now().strftime("%Y%m%d_%H%M%S")
    single_folder = f"results/历史记录/300454_深信服_{timestamp1}"
    os.makedirs(single_folder, exist_ok=True)
    
    df_single = pd.DataFrame(test_data_single)
    df_single.to_excel(f"{single_folder}/company_single_pdf.xlsx", index=False)
    
    # 模拟多个公司的结果
    import time
    time.sleep(1)  # 确保时间戳不同
    timestamp2 = datetime.now().strftime("%Y%m%d_%H%M%S")
    multiple_folder = f"results/历史记录/批量查询_{timestamp2}"
    os.makedirs(multiple_folder, exist_ok=True)
    
    df_multiple = pd.DataFrame(test_data_multiple)
    df_multiple.to_excel(f"{multiple_folder}/company_single_pdf.xlsx", index=False)
    
    # 在默认位置也保存最新的文件
    df_multiple.to_excel("results/excel/company_single_pdf.xlsx", index=False)
    
    print("✅ 测试文件结构已创建")
    print(f"📁 单个公司结果: {single_folder}")
    print(f"📁 多个公司结果: {multiple_folder}")
    print("📁 默认位置: results/excel/")
    
    # 显示目录结构
    print("\n📂 目录结构:")
    for root, dirs, files in os.walk("results"):
        level = root.replace("results", "").count(os.sep)
        indent = " " * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = " " * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")

if __name__ == "__main__":
    create_test_structure()
