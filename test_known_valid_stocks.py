"""
测试已知有效的股票代码
"""
import requests
import json
import time
import re

def test_stock_with_new_format(stock_code, exchange_info):
    """使用新格式测试股票"""
    url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    
    column, plate = exchange_info
    
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Connection': 'close',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin': 'http://www.cninfo.com.cn',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
    }
    
    data = {
        'stock': stock_code,
        'tabName': 'fulltext',
        'pageSize': '30',
        'pageNum': '1',
        'column': column,
        'plate': plate,
        'category': 'category_ndbg_szsh',  # 年报类别
        'seDate': '2020-01-01~2024-12-31',  # 更宽的时间范围
        'searchkey': '',
        'secid': '',
        'sortName': '',
        'sortType': '',
        'isHLtitle': 'true',
    }
    
    try:
        print(f"测试股票 {stock_code} ({column}-{plate}):")
        response = requests.post(url, headers=headers, data=data, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            total_announcement = result.get('totalAnnouncement', 0)
            total_records = result.get('totalRecordNum', 0)
            
            print(f"  totalAnnouncement: {total_announcement}")
            print(f"  totalRecordNum: {total_records}")
            
            if 'announcements' in result and result['announcements']:
                announcements = result['announcements']
                print(f"  找到 {len(announcements)} 条公告")
                
                # 查找年报
                annual_reports = []
                for announcement in announcements:
                    title = announcement.get('announcementTitle', '')
                    if '年度报告' in title or '年报' in title:
                        # 提取年份
                        try:
                            year_match = re.search(r'\d{4}(?=年)', title)
                            year = year_match.group(0) if year_match else 'Unknown'
                        except:
                            year = 'Unknown'
                        annual_reports.append((announcement, year))
                
                print(f"  其中年报相关: {len(annual_reports)} 条")
                for i, (report, year) in enumerate(annual_reports[:3]):
                    title = report.get('announcementTitle', 'N/A')
                    announcement_time = report.get('announcementTime', 'N/A')
                    if announcement_time != 'N/A':
                        time_array = time.localtime(int(announcement_time) / 1000)
                        formatted_time = time.strftime("%Y-%m-%d", time_array)
                    else:
                        formatted_time = 'N/A'
                    print(f"    {i+1}. [{year}年] {title}")
                    print(f"       时间: {formatted_time}")
                    
                # 如果找到年报，说明API工作正常
                if annual_reports:
                    print(f"  ✅ API工作正常！找到了年报数据")
                    return True
                    
            else:
                print("  没有找到公告数据")
        else:
            print(f"  请求失败: {response.text}")
            
    except Exception as e:
        print(f"  请求异常: {e}")
    
    print("-" * 50)
    return False

if __name__ == "__main__":
    # 测试一些知名的股票代码
    test_stocks = [
        # 股票代码, (交易所, 板块), 公司名称
        ('000001', ('szse', 'sz'), '平安银行'),
        ('000002', ('szse', 'sz'), '万科A'),
        ('600000', ('sse', 'sh'), '浦发银行'),
        ('600036', ('sse', 'sh'), '招商银行'),
        ('000858', ('szse', 'sz'), '五粮液'),
        ('600519', ('sse', 'sh'), '贵州茅台'),
        ('300059', ('szse', 'sz'), '东方财富'),  # 创业板
        ('300454', ('szse', 'sz'), '深信服'),    # 您的股票代码
    ]
    
    success_count = 0
    
    for stock_code, exchange_info, company_name in test_stocks:
        print(f"\n=== 测试 {stock_code} ({company_name}) ===")
        if test_stock_with_new_format(stock_code, exchange_info):
            success_count += 1
    
    print(f"\n=== 总结 ===")
    print(f"测试了 {len(test_stocks)} 个股票代码")
    print(f"成功获取数据: {success_count} 个")
    print(f"失败: {len(test_stocks) - success_count} 个")
    
    if success_count > 0:
        print("✅ API工作正常，可以获取到数据")
    else:
        print("❌ 所有测试都失败，可能API有问题或者需要其他参数")
