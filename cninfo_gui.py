"""
巨潮资讯网年报爬虫GUI程序
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from tkinter.ttk import Style
from ttkthemes import ThemedTk, ThemedStyle
from tkcalendar import DateEntry
import threading
import os
import sys
import requests
import random
import time
import pdfplumber
import re
import xlwt
from datetime import datetime, timedelta


class CninfoSpiderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("巨潮资讯网年报爬虫工具")
        self.root.geometry("900x800")
        self.root.minsize(800, 700)  # 设置最小窗口大小

        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重，让界面可以自适应调整
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)  # 公司代码输入区域
        main_frame.rowconfigure(7, weight=1)  # 关键词输入区域
        main_frame.rowconfigure(11, weight=3)  # 日志输出区域，给更多权重
        
        self.create_widgets(main_frame)
        
        # 初始化变量
        self.is_running = False
        
    def create_widgets(self, parent):
        row = 0
        
        # 标题
        title_label = ttk.Label(parent, text="巨潮资讯网年报爬虫工具", font=("Arial", 16, "bold"))
        title_label.grid(row=row, column=0, columnspan=2, pady=(0, 20))
        row += 1
        
        # 公司代码输入
        ttk.Label(parent, text="公司股票代码 (每行一个):").grid(row=row, column=0, sticky=tk.W, pady=5)
        row += 1
        self.company_codes_text = scrolledtext.ScrolledText(parent, height=4, width=50)
        self.company_codes_text.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        self.company_codes_text.insert("1.0", "300454\n300504\n300514")
        row += 1
        
        # 搜索关键字
        search_frame = ttk.Frame(parent)
        search_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        search_frame.columnconfigure(1, weight=1)

        ttk.Label(search_frame, text="搜索关键字:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.search_keyword = tk.StringVar(value="年度报告")
        search_entry = ttk.Entry(search_frame, textvariable=self.search_keyword, width=30)
        search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))

        ttk.Label(search_frame, text="(自动排除摘要)", foreground="gray").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        row += 1
        
        # 时间范围
        time_frame = ttk.LabelFrame(parent, text="公告发布时间范围", padding="10")
        time_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        time_frame.columnconfigure(1, weight=1)
        time_frame.columnconfigure(3, weight=1)
        row += 1

        ttk.Label(time_frame, text="开始日期:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.start_date = DateEntry(time_frame, width=12, background='darkblue',
                                   foreground='white', borderwidth=2,
                                   date_pattern='yyyy-mm-dd')
        self.start_date.set_date(datetime(2024, 1, 1))
        self.start_date.grid(row=0, column=1, padx=5, sticky=tk.W)

        ttk.Label(time_frame, text="结束日期:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5))
        self.end_date = DateEntry(time_frame, width=12, background='darkblue',
                                 foreground='white', borderwidth=2,
                                 date_pattern='yyyy-mm-dd')
        self.end_date.set_date(datetime(2025, 12, 31))
        self.end_date.grid(row=0, column=3, padx=5, sticky=tk.W)

        # 添加说明文字
        ttk.Label(time_frame, text="注意：2024年年报通常在2025年发布",
                 foreground="blue", font=("Arial", 8)).grid(row=1, column=0, columnspan=4, pady=(5, 0))
        
        # 统计关键词
        ttk.Label(parent, text="统计关键词 (每行一个):").grid(row=row, column=0, sticky=tk.W, pady=5)
        row += 1
        self.keywords_text = scrolledtext.ScrolledText(parent, height=6, width=50)
        self.keywords_text.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        default_keywords = """协同创新
合资设立
合作开发
合作研发
共同开发
联合研发
战略合作
整合资源"""
        self.keywords_text.insert("1.0", default_keywords)
        row += 1
        
        # 按钮框架
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=row, column=0, columnspan=2, pady=15)
        row += 1

        self.start_button = ttk.Button(button_frame, text="🚀 开始爬取", command=self.start_crawling,
                                      style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=10)

        self.stop_button = ttk.Button(button_frame, text="⏹ 停止", command=self.stop_crawling, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=10)

        ttk.Button(button_frame, text="📁 打开结果文件夹", command=self.open_result_folder).pack(side=tk.LEFT, padx=10)
        
        # 进度条
        progress_frame = ttk.Frame(parent)
        progress_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        progress_frame.columnconfigure(0, weight=1)

        self.progress = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))

        self.status_label = ttk.Label(progress_frame, text="就绪", foreground="green")
        self.status_label.grid(row=0, column=1, sticky=tk.E)
        row += 1

        # 日志输出
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=80, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 添加清空日志按钮
        clear_log_btn = ttk.Button(log_frame, text="清空日志", command=self.clear_log)
        clear_log_btn.grid(row=1, column=0, sticky=tk.E, pady=(5, 0))
        
    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete("1.0", tk.END)

    def update_status(self, status, color="black"):
        """更新状态显示"""
        self.status_label.config(text=status, foreground=color)
        
    def start_crawling(self):
        """开始爬取"""
        if self.is_running:
            return
            
        # 验证输入
        company_codes = self.company_codes_text.get("1.0", tk.END).strip()
        if not company_codes:
            messagebox.showerror("错误", "请输入公司股票代码")
            return
            
        search_keyword = self.search_keyword.get().strip()
        if not search_keyword:
            messagebox.showerror("错误", "请输入搜索关键字")
            return
            
        keywords = self.keywords_text.get("1.0", tk.END).strip()
        if not keywords:
            messagebox.showerror("错误", "请输入统计关键词")
            return
        
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress.start()
        self.update_status("运行中...", "blue")

        # 清空日志
        self.log_text.delete("1.0", tk.END)
        self.log("🚀 开始爬取任务...")
        
        # 在新线程中运行爬取任务
        thread = threading.Thread(target=self.crawl_task)
        thread.daemon = True
        thread.start()
        
    def stop_crawling(self):
        """停止爬取"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress.stop()
        self.update_status("已停止", "red")
        self.log("⏹ 任务已停止")
        
    def open_result_folder(self):
        """打开结果文件夹"""
        result_folder = os.path.join(os.getcwd(), "results")
        if os.path.exists(result_folder):
            os.startfile(result_folder)
        else:
            messagebox.showinfo("提示", "结果文件夹不存在")
            
    def crawl_task(self):
        """爬取任务主函数"""
        try:
            # 创建结果目录
            self.create_directories()
            
            # 获取参数
            company_codes = [code.strip() for code in self.company_codes_text.get("1.0", tk.END).strip().split('\n') if code.strip()]
            search_keyword = self.search_keyword.get().strip()
            start_date = self.start_date.get_date().strftime('%Y-%m-%d')
            end_date = self.end_date.get_date().strftime('%Y-%m-%d')
            keywords = [kw.strip() for kw in self.keywords_text.get("1.0", tk.END).strip().split('\n') if kw.strip()]
            
            self.log(f"准备爬取 {len(company_codes)} 个公司的数据")
            self.log(f"搜索关键字: {search_keyword}")
            self.log(f"时间范围: {start_date} 到 {end_date}")
            self.log(f"统计关键词: {len(keywords)} 个")
            
            # 执行爬取流程
            self.execute_crawling(company_codes, search_keyword, start_date, end_date, keywords)
            
        except Exception as e:
            self.log(f"❌ 爬取过程中出现错误: {str(e)}")
            self.update_status("错误", "red")
        finally:
            if self.is_running:  # 只有在正常完成时才显示完成状态
                self.update_status("完成", "green")
            self.stop_crawling()
            
    def create_directories(self):
        """创建必要的目录"""
        dirs = ['results', 'results/pdf', 'results/txt', 'results/excel']
        for dir_name in dirs:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
                
    def execute_crawling(self, company_codes, search_keyword, start_date, end_date, keywords):
        """执行爬取流程"""
        # 1. 爬取PDF
        pdf_files = self.crawl_pdfs(company_codes, search_keyword, start_date, end_date)

        if not self.is_running:
            return

        # 2. 转换PDF为文本
        txt_files = self.convert_pdfs_to_txt(pdf_files)

        if not self.is_running:
            return

        # 3. 统计关键词
        self.analyze_keywords(txt_files, keywords)

        self.log("✅ 所有任务完成！")

    def get_orgid_by_code(self, stock_code):
        """根据股票代码获取orgId"""
        try:
            orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            response = requests.get(orgid_url, headers=headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                stock_lists = data.get('stockList', [])
                for stock_info in stock_lists:
                    if stock_info.get('code') == stock_code:
                        return {
                            'code': stock_info['code'],
                            'orgId': stock_info['orgId'],
                            'zwjc': stock_info.get('zwjc', ''),
                        }
            return None
        except Exception as e:
            self.log(f"获取 {stock_code} orgId失败: {e}")
            return None

    def crawl_pdfs(self, company_codes, search_keyword, start_date, end_date):
        """爬取PDF文件"""
        self.log("开始爬取PDF文件...")
        pdf_files = []

        User_Agent = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]

        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'http://www.cninfo.com.cn',
            'X-Requested-With': 'XMLHttpRequest'
        }

        for i, stock_code in enumerate(company_codes, 1):
            if not self.is_running:
                break

            self.log(f"[{i}/{len(company_codes)}] 处理股票: {stock_code}")

            # 获取orgId
            stock_info = self.get_orgid_by_code(stock_code)
            if not stock_info:
                self.log(f"  跳过 {stock_code}: 无法获取orgId")
                continue

            orgId = stock_info['orgId']
            company_name = stock_info['zwjc']
            self.log(f"  公司: {company_name}")

            # 查询公告
            try:
                query_url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
                headers['User-Agent'] = random.choice(User_Agent)

                query_data = {
                    'pageNum': 1,
                    'pageSize': 30,
                    'tabName': 'fulltext',
                    'column': 'szse',
                    'stock': f'{stock_code},{orgId}',
                    'searchkey': '',
                    'secid': '',
                    'plate': 'sz',
                    'category': 'category_ndbg_szsh',
                    'trade': '',
                    'seDate': f'{start_date}~{end_date}',
                    'sortName': '',
                    'sortType': '',
                    'isHLtitle': 'true'
                }

                response = requests.post(query_url, headers=headers, data=query_data, timeout=10)
                result = response.json()
                announcements = result.get('announcements', [])

                self.log(f"  找到 {len(announcements)} 条公告")

                # 筛选和下载
                downloaded = 0
                for announcement in announcements:
                    if not self.is_running:
                        break

                    title = announcement.get('announcementTitle', '')

                    # 检查是否包含搜索关键字且不是摘要
                    if search_keyword in title and '摘要' not in title and '确认意见' not in title:
                        self.log(f"    准备下载: {title}")

                        # 下载PDF
                        adjunct_url = announcement.get('adjunctUrl', '')
                        if adjunct_url:
                            download_url = f'http://static.cninfo.com.cn/{adjunct_url}'
                            file_name = f"{stock_code}_{company_name}_{title}.pdf"
                            file_path = os.path.join('results/pdf', file_name)

                            if self.download_file(download_url, file_path):
                                pdf_files.append(file_path)
                                downloaded += 1
                                self.log(f"    ✅ 下载成功: {file_name}")
                            else:
                                self.log(f"    ❌ 下载失败: {file_name}")

                self.log(f"  {stock_code} 完成，下载了 {downloaded} 个文件")
                time.sleep(random.uniform(1, 3))  # 随机延时

            except Exception as e:
                self.log(f"  {stock_code} 查询失败: {e}")

        self.log(f"PDF爬取完成，共下载 {len(pdf_files)} 个文件")
        return pdf_files

    def download_file(self, url, file_path):
        """下载文件"""
        try:
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                return True
            return False
        except Exception as e:
            return False

    def convert_pdfs_to_txt(self, pdf_files):
        """将PDF转换为文本"""
        self.log("开始转换PDF为文本...")
        txt_files = []

        for i, pdf_path in enumerate(pdf_files, 1):
            if not self.is_running:
                break

            self.log(f"[{i}/{len(pdf_files)}] 转换: {os.path.basename(pdf_path)}")

            txt_path = pdf_path.replace('results/pdf/', 'results/txt/').replace('.pdf', '.txt')

            if self.extract_text_from_pdf(pdf_path, txt_path):
                txt_files.append(txt_path)
                self.log(f"  ✅ 转换成功")
            else:
                self.log(f"  ❌ 转换失败")

        self.log(f"PDF转换完成，共转换 {len(txt_files)} 个文件")
        return txt_files

    def extract_text_from_pdf(self, pdf_path, txt_path):
        """从PDF提取文本"""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"

            if text:
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                return True
            return False
        except Exception as e:
            return False

    def analyze_keywords(self, txt_files, keywords):
        """分析关键词"""
        self.log("开始关键词统计分析...")

        # 统计数据
        stat_all = {}  # 公司级别统计
        stat_sig = {}  # 单文件统计

        for i, txt_path in enumerate(txt_files, 1):
            if not self.is_running:
                break

            self.log(f"[{i}/{len(txt_files)}] 分析: {os.path.basename(txt_path)}")

            try:
                with open(txt_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 清理文本，只保留中文字符
                content = re.sub(r'[^\u4e00-\u9fa5]', '', content)

                # 统计关键词
                file_stat = {}
                for keyword in keywords:
                    count = content.count(keyword)
                    file_stat[keyword] = count

                # 获取文件信息
                file_prefix = os.path.basename(txt_path)[:-4]  # 去掉.txt
                company_id = file_prefix[:6]  # 前6位作为公司代码

                # 保存单文件统计
                stat_sig[file_prefix] = file_stat

                # 合并到公司统计
                if company_id in stat_all:
                    for keyword in keywords:
                        stat_all[company_id][keyword] += file_stat[keyword]
                else:
                    stat_all[company_id] = file_stat.copy()

                self.log(f"  ✅ 分析完成，找到关键词 {sum(file_stat.values())} 次")

            except Exception as e:
                self.log(f"  ❌ 分析失败: {e}")

        # 生成Excel文件
        self.generate_excel_reports(keywords, stat_all, stat_sig)

    def generate_excel_reports(self, keywords, stat_all, stat_sig):
        """生成Excel报告"""
        self.log("生成Excel报告...")

        try:
            # 公司级别统计
            self.write_excel('results/excel/company_keywords.xls', keywords, stat_all)
            self.log("  ✅ 生成 company_keywords.xls")

            # 单文件统计
            self.write_excel('results/excel/company_single_pdf.xls', keywords, stat_sig)
            self.log("  ✅ 生成 company_single_pdf.xls")

        except Exception as e:
            self.log(f"  ❌ 生成Excel失败: {e}")

    def write_excel(self, filename, keywords, data):
        """写入Excel文件"""
        workbook = xlwt.Workbook()
        worksheet = workbook.add_sheet('Sheet1')

        # 写入表头
        worksheet.write(0, 0, 'Company/File')
        for i, keyword in enumerate(keywords):
            worksheet.write(0, i + 1, keyword)

        # 写入数据
        row = 1
        for key, values in data.items():
            worksheet.write(row, 0, key)
            for i, keyword in enumerate(keywords):
                worksheet.write(row, i + 1, values.get(keyword, 0))
            row += 1

        workbook.save(filename)


if __name__ == "__main__":
    root = tk.Tk()
    app = CninfoSpiderGUI(root)
    root.mainloop()
