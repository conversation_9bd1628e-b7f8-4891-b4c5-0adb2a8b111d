"""
巨潮资讯网年报爬虫GUI程序
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from tkcalendar import DateEntry
import threading
import os
import sys
import requests
import random
import time
import pdfplumber
import re
import xlwt
from datetime import datetime, timedelta


class CninfoSpiderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("巨潮资讯网年报爬虫工具")
        self.root.geometry("800x700")
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        self.create_widgets(main_frame)
        
        # 初始化变量
        self.is_running = False
        
    def create_widgets(self, parent):
        row = 0
        
        # 标题
        title_label = ttk.Label(parent, text="巨潮资讯网年报爬虫工具", font=("Arial", 16, "bold"))
        title_label.grid(row=row, column=0, columnspan=2, pady=(0, 20))
        row += 1
        
        # 公司代码输入
        ttk.Label(parent, text="公司股票代码:").grid(row=row, column=0, sticky=tk.W, pady=5)
        row += 1
        self.company_codes_text = scrolledtext.ScrolledText(parent, height=5, width=50)
        self.company_codes_text.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        self.company_codes_text.insert("1.0", "300454\n300504\n300514")
        row += 1
        
        # 搜索关键字
        ttk.Label(parent, text="搜索关键字:").grid(row=row, column=0, sticky=tk.W, pady=5)
        row += 1
        self.search_keyword = tk.StringVar(value="年度报告")
        search_entry = ttk.Entry(parent, textvariable=self.search_keyword, width=50)
        search_entry.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        row += 1
        
        # 时间范围
        time_frame = ttk.LabelFrame(parent, text="时间范围", padding="5")
        time_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        row += 1
        
        ttk.Label(time_frame, text="开始日期:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.start_date = DateEntry(time_frame, width=12, background='darkblue',
                                   foreground='white', borderwidth=2, 
                                   date_pattern='yyyy-mm-dd')
        self.start_date.set_date(datetime(2024, 1, 1))
        self.start_date.grid(row=0, column=1, padx=5)
        
        ttk.Label(time_frame, text="结束日期:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.end_date = DateEntry(time_frame, width=12, background='darkblue',
                                 foreground='white', borderwidth=2,
                                 date_pattern='yyyy-mm-dd')
        self.end_date.set_date(datetime(2025, 12, 31))
        self.end_date.grid(row=0, column=3, padx=5)
        
        # 统计关键词
        ttk.Label(parent, text="统计关键词:").grid(row=row, column=0, sticky=tk.W, pady=5)
        row += 1
        self.keywords_text = scrolledtext.ScrolledText(parent, height=8, width=50)
        self.keywords_text.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        default_keywords = """协同创新
合资设立
合作开发
合作研发
共同开发
联合研发
战略合作
整合资源"""
        self.keywords_text.insert("1.0", default_keywords)
        row += 1
        
        # 按钮框架
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=row, column=0, columnspan=2, pady=20)
        row += 1
        
        self.start_button = ttk.Button(button_frame, text="开始爬取", command=self.start_crawling)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止", command=self.stop_crawling, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="打开结果文件夹", command=self.open_result_folder).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(parent, mode='indeterminate')
        self.progress.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        row += 1
        
        # 日志输出
        ttk.Label(parent, text="运行日志:").grid(row=row, column=0, sticky=tk.W, pady=5)
        row += 1
        self.log_text = scrolledtext.ScrolledText(parent, height=10, width=80)
        self.log_text.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def start_crawling(self):
        """开始爬取"""
        if self.is_running:
            return
            
        # 验证输入
        company_codes = self.company_codes_text.get("1.0", tk.END).strip()
        if not company_codes:
            messagebox.showerror("错误", "请输入公司股票代码")
            return
            
        search_keyword = self.search_keyword.get().strip()
        if not search_keyword:
            messagebox.showerror("错误", "请输入搜索关键字")
            return
            
        keywords = self.keywords_text.get("1.0", tk.END).strip()
        if not keywords:
            messagebox.showerror("错误", "请输入统计关键词")
            return
        
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress.start()
        
        # 清空日志
        self.log_text.delete("1.0", tk.END)
        self.log("开始爬取任务...")
        
        # 在新线程中运行爬取任务
        thread = threading.Thread(target=self.crawl_task)
        thread.daemon = True
        thread.start()
        
    def stop_crawling(self):
        """停止爬取"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress.stop()
        self.log("任务已停止")
        
    def open_result_folder(self):
        """打开结果文件夹"""
        result_folder = os.path.join(os.getcwd(), "results")
        if os.path.exists(result_folder):
            os.startfile(result_folder)
        else:
            messagebox.showinfo("提示", "结果文件夹不存在")
            
    def crawl_task(self):
        """爬取任务主函数"""
        try:
            # 创建结果目录
            self.create_directories()
            
            # 获取参数
            company_codes = [code.strip() for code in self.company_codes_text.get("1.0", tk.END).strip().split('\n') if code.strip()]
            search_keyword = self.search_keyword.get().strip()
            start_date = self.start_date.get_date().strftime('%Y-%m-%d')
            end_date = self.end_date.get_date().strftime('%Y-%m-%d')
            keywords = [kw.strip() for kw in self.keywords_text.get("1.0", tk.END).strip().split('\n') if kw.strip()]
            
            self.log(f"准备爬取 {len(company_codes)} 个公司的数据")
            self.log(f"搜索关键字: {search_keyword}")
            self.log(f"时间范围: {start_date} 到 {end_date}")
            self.log(f"统计关键词: {len(keywords)} 个")
            
            # 执行爬取流程
            self.execute_crawling(company_codes, search_keyword, start_date, end_date, keywords)
            
        except Exception as e:
            self.log(f"爬取过程中出现错误: {str(e)}")
        finally:
            self.stop_crawling()
            
    def create_directories(self):
        """创建必要的目录"""
        dirs = ['results', 'results/pdf', 'results/txt', 'results/excel']
        for dir_name in dirs:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
                
    def execute_crawling(self, company_codes, search_keyword, start_date, end_date, keywords):
        """执行爬取流程"""
        # 这里会调用实际的爬取逻辑
        # 由于代码较长，我会在下一个文件中继续实现
        pass


if __name__ == "__main__":
    root = tk.Tk()
    app = CninfoSpiderGUI(root)
    root.mainloop()
