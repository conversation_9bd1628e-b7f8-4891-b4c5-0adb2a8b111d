"""
CustomTkinter版本打包脚本
"""
import os
import sys
import subprocess
import site

def find_customtkinter_path():
    """查找customtkinter包的路径"""
    try:
        import customtkinter
        ctk_path = os.path.dirname(customtkinter.__file__)
        return ctk_path
    except ImportError:
        print("未找到customtkinter包")
        return None

def build_executable():
    """构建可执行文件"""
    print("开始打包巨潮年报爬虫工具 (CustomTkinter版本)...")
    
    # 基本的PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包成单个文件
        '--windowed',  # 不显示控制台窗口
        '--name=巨潮年报爬虫工具_CTK',  # 可执行文件名称
        '--hidden-import=customtkinter',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkcalendar',
        '--hidden-import=pdfplumber',
        '--hidden-import=requests',
        '--hidden-import=xlwt',
        '--hidden-import=babel.dates',
        '--hidden-import=PIL',
        '--hidden-import=darkdetect',
        '--collect-all=customtkinter',
        'cninfo_gui_ctk.py'
    ]
    
    # 查找并添加customtkinter数据文件
    ctk_path = find_customtkinter_path()
    if ctk_path:
        print(f"找到customtkinter路径: {ctk_path}")
        # CustomTkinter需要包含assets文件夹
        assets_path = os.path.join(ctk_path, 'assets')
        if os.path.exists(assets_path):
            cmd.insert(-1, f'--add-data={assets_path};customtkinter/assets')
    else:
        print("警告: 未找到customtkinter，可能影响界面显示")
    
    try:
        # 执行打包命令
        print("执行打包命令...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        print("可执行文件位置: dist/巨潮年报爬虫工具_CTK.exe")
        
        # 创建使用说明
        create_readme()
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        
def create_readme():
    """创建使用说明文件"""
    readme_content = """
# 巨潮年报爬虫工具使用说明 (CustomTkinter版本)

## 功能介绍
这是一个用于从巨潮资讯网爬取上市公司年报并进行关键词统计分析的现代化工具。

## 界面特色
- 🎨 现代化深色主题界面
- 📊 实时进度显示和状态反馈
- 📋 清晰的运行日志
- 🔍 智能关键词搜索
- 📱 响应式布局设计

## 使用方法

### 1. 启动程序
双击 `巨潮年报爬虫工具_CTK.exe` 启动程序

### 2. 填写参数

#### 📈 公司股票代码
- 在第一个文本框中输入要爬取的股票代码
- 每行一个代码，例如：
  ```
  300454
  300504
  300514
  ```

#### 🔍 搜索关键字
- 输入要搜索的报告类型关键字
- 例如：`年度报告`（会自动搜索包含此关键字的文件）
- 程序会自动排除摘要文件

#### 📅 时间范围
- 设置公告发布的时间范围
- 默认为2024-01-01到2025-12-31
- 💡 注意：2024年年报通常在2025年发布

#### 🔤 统计关键词
- 输入要统计的关键词，每行一个
- 程序会统计这些关键词在年报中的出现次数

### 3. 开始爬取
点击"🚀 开始爬取"按钮，程序将：
1. 爬取PDF文件到 results/pdf/ 目录
2. 转换PDF为文本到 results/txt/ 目录  
3. 生成Excel统计报告到 results/excel/ 目录

### 4. 查看结果
- 点击"📁 打开结果文件夹"查看下载的文件
- Excel文件包含：
  - company_keywords.xls: 公司级别关键词统计
  - company_single_pdf.xls: 单个PDF文件关键词统计

### 5. 其他功能
- 🗑 清空日志：清除运行日志
- ⏹ 停止：中断正在运行的任务
- 🟢/🔵/🔴 状态指示器：显示当前运行状态

## 界面说明
- **深色主题**：护眼的深色界面设计
- **进度条**：实时显示任务进度
- **状态指示**：🟢就绪 🔵运行中 🔴停止/错误
- **滚动界面**：支持滚动查看所有内容

## 注意事项
1. 确保网络连接正常
2. 爬取过程中请勿关闭程序
3. 大量数据爬取可能需要较长时间
4. 程序会自动添加延时以避免被反爬虫

## 技术支持
如有问题，请检查运行日志中的错误信息。

---
版本: 3.0 (CustomTkinter现代化界面)
"""
    
    with open('使用说明_CTK.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 已生成使用说明_CTK.txt")

if __name__ == "__main__":
    build_executable()
