@echo off
echo ========================================
echo 正在打包巨潮年报爬虫工具 (优化版)
echo ========================================

echo 清理旧的构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo.
echo 开始打包...
pyinstaller ^
    --onefile ^
    --windowed ^
    --name="巨潮年报爬虫工具" ^
    --icon=icon.ico ^
    --hidden-import=customtkinter ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=pdfplumber ^
    --hidden-import=requests ^
    --hidden-import=xlwt ^
    --hidden-import=PIL ^
    --hidden-import=darkdetect ^
    --collect-all=customtkinter ^
    --add-data="README.md;." ^
    cninfo_gui_ctk.py

echo.
if exist "dist\巨潮年报爬虫工具.exe" (
    echo ========================================
    echo ✅ 打包成功！
    echo ========================================
    echo 可执行文件位置: dist\巨潮年报爬虫工具.exe
    echo 文件大小:
    dir "dist\巨潮年报爬虫工具.exe" | find ".exe"
    echo.
    echo 正在创建使用说明...
    echo 巨潮年报爬虫工具使用说明 > dist\使用说明.txt
    echo. >> dist\使用说明.txt
    echo 1. 双击运行 巨潮年报爬虫工具.exe >> dist\使用说明.txt
    echo 2. 输入股票代码，每行一个 >> dist\使用说明.txt
    echo 3. 设置搜索关键字和时间范围 >> dist\使用说明.txt
    echo 4. 输入统计关键词 >> dist\使用说明.txt
    echo 5. 点击开始爬取 >> dist\使用说明.txt
    echo 6. 结果保存在 results 文件夹中 >> dist\使用说明.txt
    echo.
    echo ✅ 使用说明已创建
    echo.
    echo 是否打开结果文件夹？
    set /p choice=输入 y 打开文件夹，其他键退出: 
    if /i "%choice%"=="y" start explorer "dist"
) else (
    echo ========================================
    echo ❌ 打包失败！
    echo ========================================
    echo 请检查错误信息
)

echo.
pause
