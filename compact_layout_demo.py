"""
紧凑布局演示 - 对比优化前后的界面效果
"""
import customtkinter as ctk
import tkinter as tk

def create_compact_demo():
    """创建紧凑布局演示"""
    
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    # 创建主窗口
    root = ctk.CTk()
    root.title("紧凑布局演示")
    root.geometry("1100x750+100+50")
    root.minsize(950, 650)
    
    # 创建主框架
    main_frame = ctk.CTkScrollableFrame(root, corner_radius=8)
    main_frame.pack(fill="both", expand=True, padx=15, pady=15)
    
    # 标题
    title_label = ctk.CTkLabel(
        main_frame, 
        text="🎨 紧凑布局设计演示",
        font=ctk.CTkFont(size=22, weight="bold")
    )
    title_label.pack(pady=(0, 15))
    
    # 设计原则说明
    principles_frame = ctk.CTkFrame(main_frame, corner_radius=8)
    principles_frame.pack(fill="x", pady=(0, 15))
    
    principles_title = ctk.CTkLabel(
        principles_frame,
        text="📐 紧凑设计原则",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    principles_title.pack(anchor="w", padx=15, pady=(15, 5))
    
    principles_text = """✅ 减少不必要的空白区域
✅ 优化组件间距和内边距
✅ 合理调整字体大小
✅ 紧凑但不拥挤的布局
✅ 保持良好的视觉层次
✅ 提高屏幕空间利用率"""
    
    principles_label = ctk.CTkLabel(
        principles_frame,
        text=principles_text,
        font=ctk.CTkFont(size=11),
        justify="left"
    )
    principles_label.pack(anchor="w", padx=15, pady=(0, 15))
    
    # 左右分栏演示
    demo_container = ctk.CTkFrame(main_frame, corner_radius=8)
    demo_container.pack(fill="x", pady=(10, 15))
    
    demo_title = ctk.CTkLabel(
        demo_container,
        text="📱 左右分栏布局",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    demo_title.pack(anchor="w", padx=15, pady=(15, 8))
    
    # 左右分栏容器
    lr_frame = ctk.CTkFrame(demo_container, fg_color="transparent")
    lr_frame.pack(fill="x", padx=15, pady=(0, 15))
    
    # 左侧演示
    left_demo = ctk.CTkFrame(lr_frame, corner_radius=6)
    left_demo.pack(side="left", fill="both", expand=True, padx=(0, 10))
    
    left_title = ctk.CTkLabel(left_demo, text="📈 左侧区域", font=ctk.CTkFont(size=14, weight="bold"))
    left_title.pack(anchor="w", padx=15, pady=(15, 3))
    
    left_info = ctk.CTkLabel(left_demo, text="紧凑的说明文字", font=ctk.CTkFont(size=11), text_color="gray")
    left_info.pack(anchor="w", padx=15, pady=(0, 8))
    
    left_textbox = ctk.CTkTextbox(left_demo, height=100, corner_radius=5, font=ctk.CTkFont(size=12))
    left_textbox.pack(fill="both", expand=True, padx=15, pady=(0, 15))
    left_textbox.insert("1.0", "紧凑的文本框\n减少了高度和边距\n保持功能完整")
    
    # 右侧演示
    right_demo = ctk.CTkFrame(lr_frame, corner_radius=6)
    right_demo.pack(side="right", fill="both", expand=True, padx=(10, 0))
    
    right_title = ctk.CTkLabel(right_demo, text="🔍 右侧区域", font=ctk.CTkFont(size=14, weight="bold"))
    right_title.pack(anchor="w", padx=15, pady=(15, 3))
    
    right_info = ctk.CTkLabel(right_demo, text="优化的输入区域", font=ctk.CTkFont(size=11), text_color="gray")
    right_info.pack(anchor="w", padx=15, pady=(0, 8))
    
    right_entry = ctk.CTkEntry(right_demo, placeholder_text="紧凑的输入框", font=ctk.CTkFont(size=12), height=32)
    right_entry.pack(fill="x", padx=15, pady=(0, 8))
    
    right_note = ctk.CTkLabel(right_demo, text="💡 简洁的提示信息", font=ctk.CTkFont(size=10), text_color="gray")
    right_note.pack(anchor="w", padx=15, pady=(0, 15))
    
    # 按钮区域演示
    button_demo = ctk.CTkFrame(main_frame, corner_radius=8)
    button_demo.pack(fill="x", pady=(0, 15))
    
    button_title = ctk.CTkLabel(
        button_demo,
        text="🔘 紧凑按钮布局",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    button_title.pack(anchor="w", padx=15, pady=(15, 8))
    
    # 按钮容器
    btn_frame = ctk.CTkFrame(button_demo, fg_color="transparent")
    btn_frame.pack(fill="x", padx=15, pady=(0, 15))
    
    # 紧凑按钮
    btn1 = ctk.CTkButton(btn_frame, text="🚀 主要操作", font=ctk.CTkFont(size=13, weight="bold"), 
                        height=36, width=110, corner_radius=8)
    btn1.pack(side="left", padx=(0, 10))
    
    btn2 = ctk.CTkButton(btn_frame, text="⏹ 停止", font=ctk.CTkFont(size=13), 
                        height=36, width=80, corner_radius=8, fg_color="#dc3545")
    btn2.pack(side="left", padx=(0, 10))
    
    btn3 = ctk.CTkButton(btn_frame, text="📊 查看", font=ctk.CTkFont(size=13), 
                        height=36, width=100, corner_radius=8, fg_color="#17a2b8")
    btn3.pack(side="right", padx=(0, 10))
    
    btn4 = ctk.CTkButton(btn_frame, text="📁 文件夹", font=ctk.CTkFont(size=13), 
                        height=36, width=110, corner_radius=8, fg_color="#28a745")
    btn4.pack(side="right", padx=(0, 10))
    
    # 优化效果对比
    comparison_frame = ctk.CTkFrame(main_frame, corner_radius=8)
    comparison_frame.pack(fill="x", pady=(0, 15))
    
    comp_title = ctk.CTkLabel(
        comparison_frame,
        text="📊 优化效果对比",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    comp_title.pack(anchor="w", padx=15, pady=(15, 8))
    
    comparison_text = """优化前 → 优化后
• 窗口大小: 1200x900 → 1100x750 (节省150像素高度)
• 标题字体: 28px → 22px (保持清晰度)
• 组件间距: 25-30px → 15-20px (减少空白)
• 按钮高度: 45px → 36px (更紧凑)
• 文本框高度: 180-250px → 140-180px (合理压缩)
• 边距设置: 20-25px → 15px (统一优化)

✅ 结果: 界面更紧凑，信息密度更高，视觉效果更佳"""
    
    comp_label = ctk.CTkLabel(
        comparison_frame,
        text=comparison_text,
        font=ctk.CTkFont(size=11),
        justify="left"
    )
    comp_label.pack(anchor="w", padx=15, pady=(0, 15))
    
    # 日志区域演示
    log_demo = ctk.CTkFrame(main_frame, corner_radius=8)
    log_demo.pack(fill="x", pady=(0, 15))
    
    log_title = ctk.CTkLabel(
        log_demo,
        text="📋 紧凑日志区域",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    log_title.pack(anchor="w", padx=15, pady=(15, 8))
    
    log_textbox = ctk.CTkTextbox(
        log_demo,
        height=120,
        corner_radius=6,
        font=ctk.CTkFont(size=11, family="Consolas")
    )
    log_textbox.pack(fill="x", padx=15, pady=(0, 15))
    
    log_content = """[12:34:56] 🚀 开始爬取任务...
[12:34:57] 📁 创建结果文件夹: results/history/
[12:34:58] 📈 处理股票: 300454
[12:34:59] ✅ 下载成功: 300454_深信服_2024年年度报告.pdf
[12:35:00] 📄 转换PDF为文本...
[12:35:01] 🔤 开始关键词统计分析...
[12:35:02] ✅ 所有任务完成！"""
    log_textbox.insert("1.0", log_content)
    
    print("🎨 紧凑布局演示已启动")
    print("📐 主要优化:")
    print("   - 减少窗口大小和组件间距")
    print("   - 优化字体大小和按钮尺寸")
    print("   - 提高屏幕空间利用率")
    print("   - 保持良好的视觉效果")
    
    root.mainloop()

if __name__ == "__main__":
    create_compact_demo()
