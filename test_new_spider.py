"""
测试修改后的spider API
"""
import requests
import json

def test_orgid_api():
    """测试获取orgId的API"""
    orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
    
    try:
        print("=== 测试获取orgId API ===")
        response = requests.get(orgid_url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            stock_lists = data.get('stockList', [])
            print(f"获取到 {len(stock_lists)} 个股票信息")
            
            # 查找我们的测试股票
            test_codes = ['300454', '300504', '300514']
            found_stocks = []
            
            for stock_info in stock_lists:
                code = stock_info.get('code')
                if code in test_codes:
                    found_stocks.append({
                        'code': code,
                        'orgId': stock_info.get('orgId'),
                        'zwjc': stock_info.get('zwjc', ''),
                        'ywjc': stock_info.get('ywjc', '')
                    })
            
            print(f"找到的测试股票:")
            for stock in found_stocks:
                print(f"  {stock['code']}: {stock['zwjc']} (orgId: {stock['orgId']})")
            
            return found_stocks
        else:
            print(f"获取orgId失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"获取orgId异常: {e}")
        return []

def test_query_with_orgid(stock_code, orgId, company_name):
    """使用orgId测试查询"""
    url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin': 'http://www.cninfo.com.cn',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
    }
    
    data = {
        'stock': f'{stock_code},{orgId}',
        'tabName': 'fulltext',
        'pageSize': '30',
        'pageNum': '1',
        'column': 'szse',
        'category': 'category_ndbg_szsh',
        'plate': 'sz',
        'seDate': '2022-01-01~2024-12-31',
        'searchkey': '',
        'secid': '',
        'sortName': '',
        'sortType': '',
        'isHLtitle': 'true'
    }
    
    try:
        print(f"\n=== 测试股票 {stock_code} ({company_name}) ===")
        response = requests.post(url, headers=headers, data=data, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            total_announcement = result.get('totalAnnouncement', 0)
            total_records = result.get('totalRecordNum', 0)
            
            print(f"totalAnnouncement: {total_announcement}")
            print(f"totalRecordNum: {total_records}")
            
            if 'announcements' in result and result['announcements']:
                announcements = result['announcements']
                print(f"找到 {len(announcements)} 条公告")
                
                # 查找年报
                annual_reports = []
                for announcement in announcements:
                    title = announcement.get('announcementTitle', '')
                    if '年度报告' in title or '年报' in title:
                        annual_reports.append(announcement)
                
                print(f"其中年报相关: {len(annual_reports)} 条")
                for i, report in enumerate(annual_reports[:3]):
                    title = report.get('announcementTitle', 'N/A')
                    print(f"  {i+1}. {title}")
                    
                if annual_reports:
                    print("✅ 成功找到年报数据！")
                    return True
                    
            else:
                print("没有找到公告数据")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    return False

if __name__ == "__main__":
    # 首先获取orgId
    stocks = test_orgid_api()
    
    if stocks:
        print(f"\n开始测试查询API...")
        success_count = 0
        
        for stock in stocks:
            if test_query_with_orgid(stock['code'], stock['orgId'], stock['zwjc']):
                success_count += 1
        
        print(f"\n=== 测试结果 ===")
        print(f"测试股票数: {len(stocks)}")
        print(f"成功获取数据: {success_count}")
        
        if success_count > 0:
            print("🎉 新的API格式工作正常！可以开始爬取数据了")
        else:
            print("😞 仍然无法获取数据")
    else:
        print("❌ 无法获取股票orgId信息")
