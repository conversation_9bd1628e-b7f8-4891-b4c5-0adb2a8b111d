"""
测试鼠标滚轮修复功能
"""
import customtkinter as ctk
import tkinter as tk

def test_mousewheel_behavior():
    """测试鼠标滚轮行为"""
    
    # 设置主题
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    # 创建测试窗口
    root = ctk.CTk()
    root.title("鼠标滚轮测试")
    root.geometry("800x600+100+50")
    
    # 创建滚动框架
    main_frame = ctk.CTkScrollableFrame(root, corner_radius=10)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # 标题
    title = ctk.CTkLabel(main_frame, text="🖱️ 鼠标滚轮测试", font=ctk.CTkFont(size=24, weight="bold"))
    title.pack(pady=(0, 20))
    
    # 说明
    info = ctk.CTkLabel(main_frame, text="测试在文本框内滚动时，主界面是否会跟着滚动", 
                       font=ctk.CTkFont(size=14), text_color="gray")
    info.pack(pady=(0, 20))
    
    # 绑定鼠标滚轮事件的方法
    def bind_mousewheel_to_textbox(textbox):
        """绑定鼠标滚轮事件到文本框，阻止传播到主界面"""
        def on_mousewheel(event):
            # 阻止事件传播到父组件
            return "break"
        
        def on_enter(event):
            # 鼠标进入文本框时，绑定滚轮事件
            textbox.bind_all("<MouseWheel>", on_mousewheel)
            
        def on_leave(event):
            # 鼠标离开文本框时，解绑滚轮事件
            textbox.unbind_all("<MouseWheel>")
        
        # 绑定鼠标进入和离开事件
        textbox.bind("<Enter>", on_enter)
        textbox.bind("<Leave>", on_leave)
    
    # 创建测试文本框1
    label1 = ctk.CTkLabel(main_frame, text="📝 测试文本框1 (已修复滚轮)", 
                         font=ctk.CTkFont(size=16, weight="bold"))
    label1.pack(anchor="w", pady=(20, 5))
    
    textbox1 = ctk.CTkTextbox(main_frame, height=150, corner_radius=8)
    textbox1.pack(fill="x", pady=(0, 20))
    
    # 添加测试内容
    test_content1 = """这是测试文本框1
在这个文本框内滚动鼠标滚轮时
主界面应该不会跟着滚动
这样可以避免界面跳动
提供更好的用户体验

你可以在这里输入更多内容来测试滚动效果
测试行1
测试行2
测试行3
测试行4
测试行5
测试行6
测试行7
测试行8
测试行9
测试行10"""
    textbox1.insert("1.0", test_content1)
    
    # 绑定滚轮事件
    bind_mousewheel_to_textbox(textbox1)
    
    # 创建测试文本框2
    label2 = ctk.CTkLabel(main_frame, text="📝 测试文本框2 (未修复滚轮)", 
                         font=ctk.CTkFont(size=16, weight="bold"))
    label2.pack(anchor="w", pady=(20, 5))
    
    textbox2 = ctk.CTkTextbox(main_frame, height=150, corner_radius=8)
    textbox2.pack(fill="x", pady=(0, 20))
    
    # 添加测试内容
    test_content2 = """这是测试文本框2
在这个文本框内滚动鼠标滚轮时
主界面可能会跟着滚动
这是未修复的行为

对比测试：
- 在文本框1中滚动：主界面不动
- 在文本框2中滚动：主界面可能跟着动
- 在空白区域滚动：主界面正常滚动

测试行1
测试行2
测试行3
测试行4
测试行5
测试行6
测试行7
测试行8
测试行9
测试行10"""
    textbox2.insert("1.0", test_content2)
    
    # 不绑定滚轮事件，保持原始行为
    
    # 添加更多内容来测试主界面滚动
    for i in range(10):
        test_label = ctk.CTkLabel(main_frame, text=f"🔸 测试区域 {i+1} - 用于测试主界面滚动", 
                                 font=ctk.CTkFont(size=12))
        test_label.pack(anchor="w", pady=5)
    
    # 使用说明
    usage_frame = ctk.CTkFrame(main_frame, corner_radius=8)
    usage_frame.pack(fill="x", pady=20)
    
    usage_title = ctk.CTkLabel(usage_frame, text="📋 使用说明", 
                              font=ctk.CTkFont(size=14, weight="bold"))
    usage_title.pack(anchor="w", padx=20, pady=(15, 5))
    
    usage_text = """1. 在文本框1中滚动鼠标滚轮 → 主界面不应该滚动
2. 在文本框2中滚动鼠标滚轮 → 主界面可能会滚动
3. 在空白区域滚动鼠标滚轮 → 主界面正常滚动
4. 观察两个文本框的行为差异"""
    
    usage_label = ctk.CTkLabel(usage_frame, text=usage_text, 
                              font=ctk.CTkFont(size=12), 
                              justify="left")
    usage_label.pack(anchor="w", padx=20, pady=(0, 15))
    
    print("🖱️ 鼠标滚轮测试窗口已创建")
    print("📋 测试说明:")
    print("   1. 在文本框1中滚动 → 主界面不动（已修复）")
    print("   2. 在文本框2中滚动 → 主界面可能跟着动（未修复）")
    print("   3. 在空白区域滚动 → 主界面正常滚动")
    print("   4. 对比两个文本框的行为差异")
    
    root.mainloop()

if __name__ == "__main__":
    test_mousewheel_behavior()
