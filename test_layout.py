"""
测试新的布局改进
"""
import customtkinter as ctk
import time

def test_layout():
    """测试布局功能"""
    
    # 设置主题
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    # 创建测试窗口
    root = ctk.CTk()
    root.title("布局测试")
    root.geometry("1200x900+100+50")  # 测试窗口位置
    
    # 创建滚动框架
    main_frame = ctk.CTkScrollableFrame(root, corner_radius=10)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # 标题
    title = ctk.CTkLabel(main_frame, text="🏢 布局测试", font=ctk.CTkFont(size=28, weight="bold"))
    title.pack(pady=(0, 30))
    
    # 测试同一行布局
    container = ctk.CTkFrame(main_frame, corner_radius=10)
    container.pack(fill="x", pady=(20, 25))
    
    left_right_frame = ctk.CTkFrame(container, fg_color="transparent")
    left_right_frame.pack(fill="x", padx=25, pady=25)
    
    # 左侧框架
    left_frame = ctk.CTkFrame(left_right_frame, corner_radius=8)
    left_frame.pack(side="left", fill="both", expand=True, padx=(0, 15))
    
    left_label = ctk.CTkLabel(left_frame, text="📈 左侧内容", font=ctk.CTkFont(size=16, weight="bold"))
    left_label.pack(anchor="w", padx=20, pady=(20, 5))
    
    left_text = ctk.CTkTextbox(left_frame, height=120, corner_radius=6)
    left_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))
    left_text.insert("1.0", "这是左侧的文本框\n可以输入多行内容")
    
    # 右侧框架
    right_frame = ctk.CTkFrame(left_right_frame, corner_radius=8)
    right_frame.pack(side="right", fill="both", expand=True, padx=(15, 0))
    
    right_label = ctk.CTkLabel(right_frame, text="🔍 右侧内容", font=ctk.CTkFont(size=16, weight="bold"))
    right_label.pack(anchor="w", padx=20, pady=(20, 5))
    
    right_entry = ctk.CTkEntry(right_frame, placeholder_text="这是右侧的输入框", height=35)
    right_entry.pack(fill="x", padx=20, pady=(10, 20))
    
    # 添加更多内容来测试滚动
    for i in range(5):
        test_frame = ctk.CTkFrame(main_frame, corner_radius=8)
        test_frame.pack(fill="x", pady=10)
        
        test_label = ctk.CTkLabel(test_frame, text=f"测试区域 {i+1}", font=ctk.CTkFont(size=14))
        test_label.pack(pady=20)
    
    # 日志区域
    log_frame = ctk.CTkFrame(main_frame, corner_radius=8)
    log_frame.pack(fill="x", pady=20)
    
    log_label = ctk.CTkLabel(log_frame, text="📋 日志区域", font=ctk.CTkFont(size=18, weight="bold"))
    log_label.pack(anchor="w", padx=20, pady=(20, 10))
    
    log_text = ctk.CTkTextbox(log_frame, height=200, corner_radius=8)
    log_text.pack(fill="x", padx=20, pady=(0, 20))
    log_text.insert("1.0", "这是日志区域\n点击按钮后会自动滚动到这里")
    
    # 测试按钮
    def test_scroll():
        """测试滚动功能"""
        try:
            if hasattr(main_frame, '_parent_canvas'):
                main_frame._parent_canvas.yview_moveto(0.8)
            root.update()
        except:
            pass
    
    scroll_btn = ctk.CTkButton(main_frame, text="🔽 滚动到日志区域", command=test_scroll)
    scroll_btn.pack(pady=20)
    
    print("✅ 布局测试窗口已创建")
    print("📍 窗口位置: 左上角偏移 (100, 50)")
    print("📐 布局特点: 左右分栏，可滚动")
    
    root.mainloop()

if __name__ == "__main__":
    test_layout()
