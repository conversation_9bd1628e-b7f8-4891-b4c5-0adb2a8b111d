"""
检查和安装程序依赖
"""
import subprocess
import sys
import importlib

def check_and_install_package(package_name, import_name=None):
    """检查并安装包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False

def main():
    """主函数"""
    print("🔍 检查程序依赖...")
    print("="*50)
    
    # 必需的依赖包
    dependencies = [
        ("customtkinter", "customtkinter"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
        ("xlrd", "xlrd"),
        ("xlwt", "xlwt"),
        ("pdfplumber", "pdfplumber"),
        ("requests", "requests"),
        ("Pillow", "PIL"),
        ("tkcalendar", "tkcalendar")
    ]
    
    failed_packages = []
    
    for package_name, import_name in dependencies:
        if not check_and_install_package(package_name, import_name):
            failed_packages.append(package_name)
    
    print("\n" + "="*50)
    
    if failed_packages:
        print("❌ 以下依赖安装失败:")
        for package in failed_packages:
            print(f"   - {package}")
        print("\n💡 请手动安装失败的依赖:")
        for package in failed_packages:
            print(f"   pip install {package}")
    else:
        print("✅ 所有依赖都已正确安装！")
        print("\n🚀 现在可以运行主程序:")
        print("   python cninfo_gui_ctk.py")
    
    print("\n📋 依赖说明:")
    print("   - customtkinter: 现代化GUI界面")
    print("   - pandas: 数据处理和Excel操作")
    print("   - openpyxl: Excel文件读写(xlsx格式)")
    print("   - xlrd: Excel文件读取(xls格式)")
    print("   - xlwt: Excel文件写入(xls格式)")
    print("   - pdfplumber: PDF文本提取")
    print("   - requests: 网络请求")
    print("   - Pillow: 图像处理")
    print("   - tkcalendar: 日期选择组件")

if __name__ == "__main__":
    main()
