"""
简化的API测试
"""
import requests
import random
import json

User_Agent = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
]

def test_simple_query(stock):
    """简化的查询测试"""
    query_path = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    
    headers = {
        'User-Agent': random.choice(User_Agent),
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': 'www.cninfo.com.cn',
        'Origin': 'http://www.cninfo.com.cn',
        'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice'
    }
    
    # 简化的查询参数
    query = {
        'pageNum': 1,
        'pageSize': 30,
        'stock': stock,
        'category': 'category_ndbg_szsh',  # 去掉分号
        'tabName': 'fulltext',
        'column': 'szse',
        'plate': 'sz'
    }
    
    try:
        print(f"测试股票 {stock} (简化查询):")
        response = requests.post(query_path, headers=headers, data=query)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据键: {list(data.keys())}")
            
            if 'announcements' in data and data['announcements']:
                announcements = data['announcements']
                print(f"找到 {len(announcements)} 条公告")
                
                for i, announcement in enumerate(announcements[:3]):
                    print(f"  {i+1}. {announcement.get('announcementTitle', 'N/A')}")
                    print(f"     时间: {announcement.get('announcementTime', 'N/A')}")
            else:
                print("没有找到公告数据")
                print(f"totalRecordNum: {data.get('totalRecordNum', 'N/A')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("-" * 50)

def test_without_category(stock):
    """不指定category的测试"""
    query_path = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    
    headers = {
        'User-Agent': random.choice(User_Agent),
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': 'www.cninfo.com.cn',
        'Origin': 'http://www.cninfo.com.cn',
        'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice'
    }
    
    # 不指定category，看看能获取到什么
    query = {
        'pageNum': 1,
        'pageSize': 30,
        'stock': stock,
        'tabName': 'fulltext',
        'column': 'szse',
        'plate': 'sz'
    }
    
    try:
        print(f"测试股票 {stock} (不指定category):")
        response = requests.post(query_path, headers=headers, data=query)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if 'announcements' in data and data['announcements']:
                announcements = data['announcements']
                print(f"找到 {len(announcements)} 条公告")
                
                for i, announcement in enumerate(announcements[:5]):
                    print(f"  {i+1}. {announcement.get('announcementTitle', 'N/A')}")
                    print(f"     时间: {announcement.get('announcementTime', 'N/A')}")
            else:
                print("没有找到公告数据")
                print(f"totalRecordNum: {data.get('totalRecordNum', 'N/A')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("-" * 50)

if __name__ == "__main__":
    # 测试一个股票代码
    test_stock = '300454'
    
    test_simple_query(test_stock)
    test_without_category(test_stock)
