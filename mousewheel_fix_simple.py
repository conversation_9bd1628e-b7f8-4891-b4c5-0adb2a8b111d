"""
简单有效的鼠标滚轮修复方案
"""
import customtkinter as ctk
import tkinter as tk

def create_mousewheel_fix_demo():
    """创建鼠标滚轮修复演示"""
    
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    root = ctk.CTk()
    root.title("鼠标滚轮修复演示")
    root.geometry("900x700+100+50")
    
    # 创建滚动框架
    main_frame = ctk.CTkScrollableFrame(root, corner_radius=10)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # 标题
    title = ctk.CTkLabel(main_frame, text="🖱️ 鼠标滚轮修复方案", font=ctk.CTkFont(size=24, weight="bold"))
    title.pack(pady=(0, 20))
    
    # 简单有效的修复方法
    def fix_mousewheel_for_textbox(textbox):
        """为文本框修复鼠标滚轮问题"""
        def on_mousewheel(event):
            # 只在文本框内容超出可视区域时处理滚动
            try:
                # 获取文本框的内部tkinter Text组件
                text_widget = textbox._textbox
                
                # 计算滚动方向
                delta = -1 * int(event.delta / 120)
                
                # 让文本框自己处理滚动
                text_widget.yview_scroll(delta, "units")
                
                # 阻止事件传播
                return "break"
            except:
                return "break"
        
        # 绑定到文本框和其内部组件
        textbox.bind("<MouseWheel>", on_mousewheel)
        if hasattr(textbox, '_textbox'):
            textbox._textbox.bind("<MouseWheel>", on_mousewheel)
    
    # 创建测试区域1 - 修复后的文本框
    frame1 = ctk.CTkFrame(main_frame, corner_radius=8)
    frame1.pack(fill="x", pady=(0, 20))
    
    label1 = ctk.CTkLabel(frame1, text="✅ 修复后的文本框", font=ctk.CTkFont(size=16, weight="bold"))
    label1.pack(anchor="w", padx=20, pady=(15, 5))
    
    textbox1 = ctk.CTkTextbox(frame1, height=120, corner_radius=6)
    textbox1.pack(fill="x", padx=20, pady=(0, 15))
    
    content1 = """这是修复后的文本框
在这里滚动鼠标滚轮时，主界面不会跟着滚动
这样可以避免界面跳动，提供更好的用户体验

测试内容：
- 第1行测试内容
- 第2行测试内容  
- 第3行测试内容
- 第4行测试内容
- 第5行测试内容
- 第6行测试内容
- 第7行测试内容
- 第8行测试内容
- 第9行测试内容
- 第10行测试内容
- 第11行测试内容
- 第12行测试内容"""
    textbox1.insert("1.0", content1)
    
    # 应用修复
    fix_mousewheel_for_textbox(textbox1)
    
    # 创建测试区域2 - 未修复的文本框
    frame2 = ctk.CTkFrame(main_frame, corner_radius=8)
    frame2.pack(fill="x", pady=(0, 20))
    
    label2 = ctk.CTkLabel(frame2, text="❌ 未修复的文本框", font=ctk.CTkFont(size=16, weight="bold"))
    label2.pack(anchor="w", padx=20, pady=(15, 5))
    
    textbox2 = ctk.CTkTextbox(frame2, height=120, corner_radius=6)
    textbox2.pack(fill="x", padx=20, pady=(0, 15))
    
    content2 = """这是未修复的文本框
在这里滚动鼠标滚轮时，主界面可能会跟着滚动
这会导致界面跳动，影响用户体验

对比测试：
- 在上面的文本框中滚动：主界面保持不动
- 在这个文本框中滚动：主界面可能跟着动
- 在空白区域滚动：主界面正常滚动

测试内容：
- 第1行测试内容
- 第2行测试内容
- 第3行测试内容
- 第4行测试内容
- 第5行测试内容
- 第6行测试内容
- 第7行测试内容
- 第8行测试内容"""
    textbox2.insert("1.0", content2)
    
    # 不应用修复，保持原始行为
    
    # 添加更多内容来测试主界面滚动
    info_frame = ctk.CTkFrame(main_frame, corner_radius=8)
    info_frame.pack(fill="x", pady=(0, 20))
    
    info_label = ctk.CTkLabel(info_frame, text="📋 测试说明", font=ctk.CTkFont(size=16, weight="bold"))
    info_label.pack(anchor="w", padx=20, pady=(15, 5))
    
    info_text = """1. 在第一个文本框中滚动鼠标滚轮 → 主界面不会滚动 ✅
2. 在第二个文本框中滚动鼠标滚轮 → 主界面可能会滚动 ❌
3. 在空白区域滚动鼠标滚轮 → 主界面正常滚动 ✅
4. 观察两个文本框的行为差异

修复原理：
- 捕获文本框的鼠标滚轮事件
- 让文本框自己处理滚动
- 阻止事件传播到父组件
- 避免主界面跟着滚动"""
    
    info_content = ctk.CTkLabel(info_frame, text=info_text, font=ctk.CTkFont(size=12), justify="left")
    info_content.pack(anchor="w", padx=20, pady=(0, 15))
    
    # 添加更多测试区域
    for i in range(8):
        test_frame = ctk.CTkFrame(main_frame, corner_radius=6)
        test_frame.pack(fill="x", pady=5)
        
        test_label = ctk.CTkLabel(test_frame, text=f"🔸 测试区域 {i+1} - 用于测试主界面滚动效果", 
                                 font=ctk.CTkFont(size=12))
        test_label.pack(pady=10)
    
    print("🖱️ 鼠标滚轮修复演示已启动")
    print("📋 请测试两个文本框的滚动行为差异")
    
    root.mainloop()

if __name__ == "__main__":
    create_mousewheel_fix_demo()
