"""
调试spider，查看实际返回的数据
"""
import requests
import random
import json

User_Agent = [
    "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Media Center PC 6.0)",
    "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN) AppleWebKit/523.15 (KHTML, like Gecko, Safari/419.3) Arora/0.3 (Change: 287 c9dfb30)",
]

headers = {'Accept': 'application/json, text/javascript, */*; q=0.01',
           "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
           "Accept-Encoding": "gzip, deflate",
           "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-HK;q=0.6,zh-TW;q=0.5",
           'Host': 'www.cninfo.com.cn',
           'Origin': 'http://www.cninfo.com.cn',
           'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice',
           'X-Requested-With': 'XMLHttpRequest'
           }

def get_orgid_by_code(stock_code):
    """根据股票代码获取orgId"""
    orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'
    try:
        response = requests.get(orgid_url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            stock_lists = data.get('stockList', [])
            for stock_info in stock_lists:
                if stock_info.get('code') == stock_code:
                    return {
                        'code': stock_info['code'],
                        'orgId': stock_info['orgId'],
                        'zwjc': stock_info.get('zwjc', ''),
                    }
        return None
    except Exception as e:
        print(f"获取orgId失败: {e}")
        return None

def debug_query(stock_code, orgId):
    """调试查询，显示所有返回的数据"""
    query_path = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    headers['User-Agent'] = random.choice(User_Agent)
    
    query = {
        'pageNum': 1,
        'pageSize': 30,
        'tabName': 'fulltext',
        'column': 'szse',
        'stock': f'{stock_code},{orgId}',
        'searchkey': '',
        'secid': '',
        'plate': 'sz',
        'category': 'category_ndbg_szsh',
        'trade': '',
        'seDate': '2023-01-01~2023-12-31',
        'sortName': '',
        'sortType': '',
        'isHLtitle': 'true'
    }

    try:
        response = requests.post(query_path, headers=headers, data=query)
        result = response.json()
        
        print(f"查询结果:")
        print(f"  totalAnnouncement: {result.get('totalAnnouncement', 0)}")
        print(f"  totalRecordNum: {result.get('totalRecordNum', 0)}")
        
        announcements = result.get('announcements', [])
        print(f"  实际公告数量: {len(announcements)}")
        
        print(f"\n所有公告详情:")
        for i, announcement in enumerate(announcements):
            title = announcement.get('announcementTitle', 'N/A')
            time = announcement.get('announcementTime', 'N/A')
            url = announcement.get('adjunctUrl', 'N/A')
            print(f"  {i+1}. {title}")
            print(f"     时间: {time}")
            print(f"     URL: {url}")
            print()
        
        return announcements
        
    except Exception as e:
        print(f"查询失败: {e}")
        return []

if __name__ == "__main__":
    stock_code = '300454'
    
    # 获取orgId
    stock_info = get_orgid_by_code(stock_code)
    if stock_info:
        print(f"股票信息: {stock_info}")
        
        # 调试查询
        announcements = debug_query(stock_code, stock_info['orgId'])
        
    else:
        print(f"无法获取股票 {stock_code} 的orgId")
