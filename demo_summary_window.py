"""
演示总结窗口功能
"""
import customtkinter as ctk
import pandas as pd
import os
from tkinter import messagebox, filedialog

# 导入总结窗口类（简化版）
class SummaryWindowDemo:
    """总结窗口演示"""
    def __init__(self, df):
        # 设置主题
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        self.df = df
        
        # 创建主窗口
        self.window = ctk.CTk()
        self.window.title("📋 关键词统计详细总结 - 演示")
        self.window.geometry("1000x700+100+50")
        self.window.minsize(800, 600)
        
        self.create_widgets()
        self.generate_summary()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ctk.CTkFrame(self.window, corner_radius=10)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="📋 关键词统计详细总结",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 说明文字
        info_label = ctk.CTkLabel(
            main_frame,
            text="以下是每个文件的关键词出现次数详细分析",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        info_label.pack(pady=(0, 20))
        
        # 创建滚动文本框
        self.summary_text = ctk.CTkTextbox(
            main_frame,
            corner_radius=8,
            font=ctk.CTkFont(size=12, family="Consolas")
        )
        self.summary_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # 按钮框架
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # 导出总结按钮
        export_btn = ctk.CTkButton(
            button_frame,
            text="📤 导出总结",
            font=ctk.CTkFont(size=14),
            height=35,
            width=120,
            fg_color="#28a745",
            hover_color="#218838",
            command=self.export_summary
        )
        export_btn.pack(side="left", padx=(0, 10))
        
        # 刷新按钮
        refresh_btn = ctk.CTkButton(
            button_frame,
            text="🔄 刷新总结",
            font=ctk.CTkFont(size=14),
            height=35,
            width=120,
            fg_color="#17a2b8",
            hover_color="#138496",
            command=self.generate_summary
        )
        refresh_btn.pack(side="left", padx=(0, 10))
        
        # 关闭按钮
        close_btn = ctk.CTkButton(
            button_frame,
            text="❌ 关闭",
            font=ctk.CTkFont(size=14),
            height=35,
            width=100,
            fg_color="#dc3545",
            hover_color="#c82333",
            command=self.window.destroy
        )
        close_btn.pack(side="right")
        
    def generate_summary(self):
        """生成详细总结"""
        try:
            summary_text = "📊 关键词统计详细总结报告\n"
            summary_text += "=" * 60 + "\n\n"
            
            # 获取文件名列和关键词列
            file_col = self.df.columns[0]  # 第一列是文件名
            keyword_cols = self.df.columns[1:]  # 其余列是关键词
            
            # 总体统计
            summary_text += "📈 总体统计:\n"
            summary_text += f"   文件总数: {len(self.df)} 个\n"
            summary_text += f"   关键词总数: {len(keyword_cols)} 个\n"
            
            # 计算总出现次数
            numeric_cols = self.df.select_dtypes(include=['number']).columns
            total_occurrences = self.df[numeric_cols].sum().sum()
            summary_text += f"   关键词总出现次数: {total_occurrences} 次\n\n"
            
            # 关键词整体统计
            summary_text += "🔤 关键词整体统计:\n"
            keyword_totals = {}
            for keyword in keyword_cols:
                if keyword in numeric_cols:
                    total = self.df[keyword].sum()
                    keyword_totals[keyword] = total
                    summary_text += f"   {keyword}: {total} 次\n"
            summary_text += "\n"
            
            # 按文件详细分析
            summary_text += "📄 按文件详细分析:\n"
            summary_text += "-" * 60 + "\n\n"
            
            for index, row in self.df.iterrows():
                file_name = row[file_col]
                summary_text += f"📁 {file_name}\n"
                
                # 统计该文件的关键词
                has_keywords = []
                zero_keywords = []
                file_total = 0
                
                for keyword in keyword_cols:
                    if keyword in row.index:
                        count = row[keyword]
                        if pd.isna(count):
                            count = 0
                        else:
                            count = int(count)
                        
                        if count > 0:
                            has_keywords.append((keyword, count))
                            file_total += count
                        else:
                            zero_keywords.append(keyword)
                
                # 显示有出现的关键词
                if has_keywords:
                    summary_text += f"   ✅ 出现的关键词 ({len(has_keywords)} 个):\n"
                    # 按出现次数排序
                    has_keywords.sort(key=lambda x: x[1], reverse=True)
                    for keyword, count in has_keywords:
                        summary_text += f"      • {keyword}: {count} 次\n"
                else:
                    summary_text += "   ❌ 没有关键词出现\n"
                
                # 显示未出现的关键词
                if zero_keywords:
                    summary_text += f"   ⭕ 未出现的关键词 ({len(zero_keywords)} 个):\n"
                    summary_text += f"      {', '.join(zero_keywords)}\n"
                
                # 文件小计
                summary_text += f"   📊 该文件关键词总计: {file_total} 次\n"
                summary_text += f"   📈 覆盖率: {len(has_keywords)}/{len(keyword_cols)} ({len(has_keywords)/len(keyword_cols)*100:.1f}%)\n"
                summary_text += "\n"
            
            # 关键词排行榜
            summary_text += "🏆 关键词出现次数排行榜:\n"
            summary_text += "-" * 30 + "\n"
            sorted_keywords = sorted(keyword_totals.items(), key=lambda x: x[1], reverse=True)
            for i, (keyword, count) in enumerate(sorted_keywords, 1):
                if count > 0:
                    summary_text += f"   {i:2d}. {keyword}: {count} 次\n"
                else:
                    summary_text += f"   {i:2d}. {keyword}: 0 次 ❌\n"
            
            summary_text += "\n"
            summary_text += "=" * 60 + "\n"
            summary_text += f"报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            # 显示总结
            self.summary_text.delete("1.0", "end")
            self.summary_text.insert("1.0", summary_text)
            
        except Exception as e:
            error_msg = f"生成总结时出错: {str(e)}\n\n请检查数据格式是否正确。"
            self.summary_text.delete("1.0", "end")
            self.summary_text.insert("1.0", error_msg)
            
    def export_summary(self):
        """导出总结到文件"""
        try:
            # 选择保存位置
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="保存总结报告"
            )
            
            if file_path:
                # 获取总结内容
                summary_content = self.summary_text.get("1.0", "end")
                
                # 保存到文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(summary_content)
                
                messagebox.showinfo("成功", f"总结报告已导出到: {file_path}")
                
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")
    
    def run(self):
        """运行演示"""
        print("📋 总结窗口演示启动")
        print("💡 功能特点:")
        print("   - 总体统计信息")
        print("   - 关键词整体统计")
        print("   - 每个文件的详细分析")
        print("   - 区分出现和未出现的关键词")
        print("   - 关键词排行榜")
        print("   - 支持导出总结报告")
        
        self.window.mainloop()

def main():
    """主函数"""
    
    # 检查测试数据
    excel_file = "results/excel/company_single_pdf.xlsx"
    if not os.path.exists(excel_file):
        print("❌ 测试数据不存在，请先运行 test_summary_feature.py")
        return
    
    # 读取测试数据
    try:
        df = pd.read_excel(excel_file, engine='openpyxl')
        print(f"✅ 加载测试数据: {len(df)} 行, {len(df.columns)} 列")
        
        # 启动演示
        demo = SummaryWindowDemo(df)
        demo.run()
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")

if __name__ == "__main__":
    main()
