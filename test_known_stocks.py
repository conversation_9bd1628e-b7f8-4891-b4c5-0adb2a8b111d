"""
测试已知的大公司股票代码
"""
import requests
import random
import json

def test_known_stock(stock, exchange='szse', plate='sz'):
    """测试已知股票"""
    query_path = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': 'www.cninfo.com.cn',
        'Origin': 'http://www.cninfo.com.cn',
        'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice'
    }
    
    # 使用更宽的时间范围
    query = {
        'pageNum': 1,
        'pageSize': 30,
        'stock': stock,
        'tabName': 'fulltext',
        'column': exchange,
        'plate': plate,
        'seDate': '2020-01-01+~+2024-12-31'
    }
    
    try:
        print(f"测试股票 {stock} ({exchange}):")
        response = requests.post(query_path, headers=headers, data=query)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if 'announcements' in data and data['announcements']:
                announcements = data['announcements']
                print(f"找到 {len(announcements)} 条公告")
                
                # 查找年报
                annual_reports = []
                for announcement in announcements:
                    title = announcement.get('announcementTitle', '')
                    if '年度报告' in title or '年报' in title:
                        annual_reports.append(announcement)
                
                print(f"其中年报相关: {len(annual_reports)} 条")
                for i, report in enumerate(annual_reports[:3]):
                    print(f"  {i+1}. {report.get('announcementTitle', 'N/A')}")
                    print(f"     时间: {report.get('announcementTime', 'N/A')}")
                    
            else:
                print("没有找到公告数据")
                print(f"totalRecordNum: {data.get('totalRecordNum', 'N/A')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("-" * 50)

if __name__ == "__main__":
    # 测试一些知名的股票代码
    test_stocks = [
        ('000001', 'szse', 'sz'),  # 平安银行 - 深市
        ('000002', 'szse', 'sz'),  # 万科A - 深市  
        ('600000', 'sse', 'sh'),   # 浦发银行 - 沪市
        ('600036', 'sse', 'sh'),   # 招商银行 - 沪市
        ('300454', 'szse', 'sz'),  # 您的股票代码
    ]
    
    for stock, exchange, plate in test_stocks:
        test_known_stock(stock, exchange, plate)
