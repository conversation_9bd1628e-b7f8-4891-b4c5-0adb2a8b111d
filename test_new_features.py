"""
测试新功能：文件存在检查和关键词分析
"""
import os
import shutil

def create_test_files():
    """创建测试文件"""
    print("🧪 创建测试文件...")
    
    # 创建目录
    os.makedirs("results/pdf", exist_ok=True)
    os.makedirs("results/txt", exist_ok=True)
    os.makedirs("results/excel", exist_ok=True)
    
    # 创建测试PDF文件（空文件，仅用于测试存在性检查）
    test_pdf_files = [
        "results/pdf/300454_深信服_2024年年度报告.pdf",
        "results/pdf/300504_天邑股份_2024年年度报告.pdf"
    ]
    
    for pdf_file in test_pdf_files:
        if not os.path.exists(pdf_file):
            with open(pdf_file, 'w') as f:
                f.write("# Test PDF file")
            print(f"✅ 创建测试PDF: {os.path.basename(pdf_file)}")
        else:
            print(f"📁 PDF文件已存在: {os.path.basename(pdf_file)}")
    
    # 创建测试TXT文件
    test_txt_files = [
        ("results/txt/300454_深信服_2024年年度报告.txt", """
深信服科技股份有限公司2024年年度报告

公司致力于协同创新，通过合作开发新技术，推进战略合作伙伴关系。
本年度公司在合作研发方面取得重大突破，与多家企业建立了联合研发机制。
通过整合资源，公司实现了技术创新和市场拓展的双重目标。
公司将继续深化合资设立的子公司运营，推动共同开发项目的实施。
"""),
        ("results/txt/300504_天邑股份_2024年年度报告.txt", """
天邑股份有限公司2024年年度报告

公司专注于战略合作，通过协同创新模式，加强与行业伙伴的合作开发。
本年度在合作研发领域投入大量资源，建立了多个联合研发中心。
公司通过整合资源，优化产业链布局，提升核心竞争力。
未来将继续推进共同开发项目，探索合资设立新的业务板块。
""")
    ]
    
    for txt_file, content in test_txt_files:
        if not os.path.exists(txt_file):
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 创建测试TXT: {os.path.basename(txt_file)}")
        else:
            print(f"📁 TXT文件已存在: {os.path.basename(txt_file)}")

def test_file_existence_check():
    """测试文件存在性检查功能"""
    print("\n🔍 测试文件存在性检查...")
    
    pdf_dir = "results/pdf"
    txt_dir = "results/txt"
    
    if os.path.exists(pdf_dir):
        pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]
        print(f"📁 PDF文件目录存在，包含 {len(pdf_files)} 个文件")
        for pdf_file in pdf_files:
            print(f"   - {pdf_file}")
    else:
        print("❌ PDF文件目录不存在")
    
    if os.path.exists(txt_dir):
        txt_files = [f for f in os.listdir(txt_dir) if f.endswith('.txt')]
        print(f"📁 TXT文件目录存在，包含 {len(txt_files)} 个文件")
        for txt_file in txt_files:
            print(f"   - {txt_file}")
    else:
        print("❌ TXT文件目录不存在")

def test_keyword_analysis():
    """测试关键词分析功能"""
    print("\n🔤 测试关键词分析功能...")
    
    # 模拟关键词列表
    keywords = [
        "协同创新",
        "合资设立", 
        "合作开发",
        "合作研发",
        "共同开发",
        "联合研发",
        "战略合作",
        "整合资源"
    ]
    
    txt_dir = "results/txt"
    if not os.path.exists(txt_dir):
        print("❌ TXT目录不存在")
        return
    
    txt_files = [f for f in os.listdir(txt_dir) if f.endswith('.txt')]
    if not txt_files:
        print("❌ 没有找到TXT文件")
        return
    
    print(f"📊 分析 {len(txt_files)} 个文件，{len(keywords)} 个关键词")
    
    # 统计结果
    results = {}
    
    for txt_file in txt_files:
        file_path = os.path.join(txt_dir, txt_file)
        file_prefix = txt_file[:-4]  # 去掉.txt
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 统计关键词
            file_stats = {}
            for keyword in keywords:
                count = content.count(keyword)
                file_stats[keyword] = count
            
            results[file_prefix] = file_stats
            
            total_count = sum(file_stats.values())
            print(f"   📄 {file_prefix}: 总计 {total_count} 次")
            
        except Exception as e:
            print(f"   ❌ 分析失败 {txt_file}: {e}")
    
    # 显示详细结果
    print("\n📋 详细统计结果:")
    for file_name, stats in results.items():
        print(f"\n📄 {file_name}:")
        for keyword, count in stats.items():
            if count > 0:
                print(f"   {keyword}: {count} 次")

def show_feature_summary():
    """显示新功能总结"""
    print("\n" + "="*60)
    print("🎯 新功能总结")
    print("="*60)
    
    print("\n1. 📁 文件存在性检查:")
    print("   - 下载PDF前检查本地是否已存在")
    print("   - 转换TXT前检查本地是否已存在")
    print("   - 避免重复下载和转换，节省时间")
    
    print("\n2. 🔤 关键词分析按钮:")
    print("   - 独立的关键词分析功能")
    print("   - 仅对已有的TXT文件进行分析")
    print("   - 可以修改关键词后重新分析")
    print("   - 不需要重新下载和转换")
    
    print("\n3. 🚀 使用场景:")
    print("   - 首次运行：完整爬取 → 下载PDF → 转换TXT → 关键词分析")
    print("   - 修改关键词：直接点击'关键词分析'按钮")
    print("   - 增量更新：自动跳过已存在的文件")
    
    print("\n4. 💡 优势:")
    print("   - 提高效率，避免重复工作")
    print("   - 灵活的关键词调整")
    print("   - 更好的用户体验")

if __name__ == "__main__":
    print("🧪 测试新功能：文件存在检查和关键词分析")
    print("="*60)
    
    create_test_files()
    test_file_existence_check()
    test_keyword_analysis()
    show_feature_summary()
    
    print(f"\n✅ 测试完成！现在可以启动GUI程序测试新功能。")
    print("💡 提示：")
    print("   1. 启动程序后，如果有TXT文件，可以直接点击'关键词分析'")
    print("   2. 修改关键词后，再次点击'关键词分析'进行重新统计")
    print("   3. 程序会自动跳过已存在的PDF和TXT文件")
