"""
创建测试数据来演示数据展示功能
"""
import pandas as pd
import os

def create_test_data():
    """创建测试数据"""
    # 确保目录存在
    os.makedirs("results/excel", exist_ok=True)
    
    # 创建测试数据
    test_data = {
        '文件名': [
            '300454_深信服_2024年年度报告',
            '300504_天邑股份_2024年年度报告',
            '300514_友讯达_2024年年度报告',
            '300454_深信服_2023年年度报告',
            '300504_天邑股份_2023年年度报告'
        ],
        '协同创新': [15, 8, 12, 10, 6],
        '合资设立': [3, 5, 2, 4, 3],
        '合作开发': [22, 18, 15, 20, 12],
        '合作研发': [18, 12, 9, 16, 8],
        '共同开发': [7, 4, 6, 5, 3],
        '联合研发': [12, 9, 8, 11, 7],
        '战略合作': [25, 20, 18, 22, 15],
        '整合资源': [8, 6, 5, 7, 4]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    with pd.ExcelWriter('results/excel/company_single_pdf.xlsx', engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='统计结果', index=False)
        
        # 获取工作表对象进行格式化
        worksheet = writer.sheets['统计结果']
        
        # 设置列宽
        worksheet.column_dimensions['A'].width = 40  # 文件名列更宽
        for i in range(2, len(df.columns) + 1):
            col_letter = chr(64 + i)  # B, C, D...
            worksheet.column_dimensions[col_letter].width = 12
    
    print("✅ 测试数据已创建: results/excel/company_single_pdf.xlsx")
    print(f"📊 数据包含 {len(df)} 行，{len(df.columns)-1} 个关键词")

if __name__ == "__main__":
    create_test_data()
