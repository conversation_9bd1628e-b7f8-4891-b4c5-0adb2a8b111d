"""
测试总结功能
"""
import pandas as pd
import os
from datetime import datetime

def create_test_summary_data():
    """创建测试总结数据"""
    
    # 确保目录存在
    os.makedirs("results/excel", exist_ok=True)
    
    print("📊 创建测试总结数据...")
    
    # 创建包含不同情况的测试数据
    test_data = {
        '文件名': [
            '300454_深信服_2024年年度报告',
            '300504_天邑股份_2024年年度报告', 
            '300514_友讯达_2024年年度报告',
            '000001_平安银行_2024年年度报告',
            '000002_万科A_2024年年度报告'
        ],
        '协同创新': [15, 8, 0, 12, 5],      # 有些为0
        '合资设立': [3, 0, 2, 0, 1],        # 有些为0
        '合作开发': [22, 18, 15, 0, 12],    # 有些为0
        '合作研发': [18, 12, 9, 16, 0],     # 有些为0
        '共同开发': [7, 4, 6, 5, 3],        # 都有值
        '联合研发': [0, 9, 8, 11, 7],       # 有些为0
        '战略合作': [25, 20, 18, 22, 15],   # 都有值，最高频
        '整合资源': [0, 0, 5, 7, 4]         # 有些为0
    }
    
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    excel_file = "results/excel/company_single_pdf.xlsx"
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='统计结果', index=False)
        
        # 获取工作表对象进行格式化
        worksheet = writer.sheets['统计结果']
        
        # 设置列宽
        worksheet.column_dimensions['A'].width = 40  # 文件名列更宽
        for i in range(2, len(df.columns) + 1):
            col_letter = chr(64 + i)  # B, C, D...
            worksheet.column_dimensions[col_letter].width = 12
    
    print(f"✅ 测试数据已创建: {excel_file}")
    print(f"📋 数据特点:")
    print(f"   - {len(df)} 个文件")
    print(f"   - {len(df.columns)-1} 个关键词")
    print(f"   - 包含0值情况，便于测试总结功能")
    
    return excel_file

def preview_summary_logic():
    """预览总结逻辑"""
    
    print("\n🔍 预览总结逻辑...")
    
    # 读取测试数据
    excel_file = "results/excel/company_single_pdf.xlsx"
    if not os.path.exists(excel_file):
        print("❌ 测试文件不存在")
        return
    
    df = pd.read_excel(excel_file, engine='openpyxl')
    
    # 获取文件名列和关键词列
    file_col = df.columns[0]
    keyword_cols = df.columns[1:]
    
    print(f"\n📊 数据概览:")
    print(f"   文件列: {file_col}")
    print(f"   关键词列: {list(keyword_cols)}")
    
    # 关键词整体统计
    print(f"\n🔤 关键词整体统计:")
    keyword_totals = {}
    for keyword in keyword_cols:
        total = df[keyword].sum()
        keyword_totals[keyword] = total
        print(f"   {keyword}: {total} 次")
    
    # 按文件分析示例（只显示前2个文件）
    print(f"\n📄 文件分析示例:")
    for index in range(min(2, len(df))):
        row = df.iloc[index]
        file_name = row[file_col]
        print(f"\n📁 {file_name}")
        
        has_keywords = []
        zero_keywords = []
        
        for keyword in keyword_cols:
            count = int(row[keyword]) if not pd.isna(row[keyword]) else 0
            if count > 0:
                has_keywords.append((keyword, count))
            else:
                zero_keywords.append(keyword)
        
        print(f"   ✅ 出现的关键词: {len(has_keywords)} 个")
        for keyword, count in has_keywords:
            print(f"      • {keyword}: {count} 次")
        
        print(f"   ⭕ 未出现的关键词: {len(zero_keywords)} 个")
        if zero_keywords:
            print(f"      {', '.join(zero_keywords)}")
    
    # 关键词排行榜
    print(f"\n🏆 关键词排行榜:")
    sorted_keywords = sorted(keyword_totals.items(), key=lambda x: x[1], reverse=True)
    for i, (keyword, count) in enumerate(sorted_keywords, 1):
        status = "✅" if count > 0 else "❌"
        print(f"   {i:2d}. {keyword}: {count} 次 {status}")

def test_summary_window():
    """测试总结窗口功能"""
    
    print(f"\n🧪 总结功能测试完成")
    print(f"📋 测试步骤:")
    print(f"   1. 启动主程序: python cninfo_gui_ctk.py")
    print(f"   2. 点击'查看结果'打开数据窗口")
    print(f"   3. 点击'详细总结'按钮")
    print(f"   4. 查看总结内容是否正确显示:")
    print(f"      - 总体统计信息")
    print(f"      - 关键词整体统计")
    print(f"      - 每个文件的详细分析")
    print(f"      - 出现和未出现的关键词")
    print(f"      - 关键词排行榜")
    print(f"   5. 测试'导出总结'功能")
    
    print(f"\n💡 预期效果:")
    print(f"   - 清晰显示每个文件的关键词统计")
    print(f"   - 区分出现次数>0和=0的关键词")
    print(f"   - 提供整体统计和排行榜")
    print(f"   - 支持导出总结报告")

def main():
    """主测试函数"""
    
    print("🧪 测试总结功能")
    print("="*50)
    
    # 1. 创建测试数据
    excel_file = create_test_summary_data()
    
    # 2. 预览总结逻辑
    preview_summary_logic()
    
    # 3. 测试说明
    test_summary_window()
    
    print(f"\n✅ 测试准备完成！")
    print(f"📁 测试文件: {excel_file}")

if __name__ == "__main__":
    main()
