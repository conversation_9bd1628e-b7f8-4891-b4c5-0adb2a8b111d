"""
创建部署包，包含可执行文件和所有必要的说明
"""
import os
import shutil
from datetime import datetime

def create_deployment_package():
    """创建完整的部署包"""
    
    # 创建部署文件夹
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"巨潮年报爬虫工具_部署包_{timestamp}"
    package_dir = f"deployment/{package_name}"
    
    os.makedirs(package_dir, exist_ok=True)
    
    print(f"📦 创建部署包: {package_dir}")
    
    # 复制可执行文件（如果存在）
    exe_file = "dist/巨潮年报爬虫工具_优化版.exe"
    if os.path.exists(exe_file):
        shutil.copy2(exe_file, package_dir)
        print("✅ 复制可执行文件")
    else:
        print("⚠️ 可执行文件不存在，请先运行打包脚本")
    
    # 创建运行环境检查脚本
    check_script = f"""@echo off
chcp 65001 >nul
echo ========================================
echo 🔍 巨潮年报爬虫工具 - 环境检查
echo ========================================

echo 📋 检查系统环境...
echo 操作系统: %OS%
echo 处理器架构: %PROCESSOR_ARCHITECTURE%
echo.

echo 🌐 检查网络连接...
ping -n 1 www.cninfo.com.cn >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 网络连接正常
) else (
    echo ❌ 网络连接失败，请检查网络设置
)

echo.
echo 📁 检查文件权限...
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    del test_write.tmp
    echo ✅ 文件写入权限正常
) else (
    echo ❌ 文件写入权限不足，请以管理员身份运行
)

echo.
echo 🚀 准备启动程序...
if exist "巨潮年报爬虫工具_优化版.exe" (
    echo ✅ 程序文件存在
    echo.
    echo 正在启动程序...
    start "" "巨潮年报爬虫工具_优化版.exe"
) else (
    echo ❌ 程序文件不存在
    echo 请确保 巨潮年报爬虫工具_优化版.exe 在同一目录下
)

echo.
pause
"""
    
    with open(f"{package_dir}/启动程序.bat", "w", encoding="utf-8") as f:
        f.write(check_script)
    
    # 创建详细的部署说明
    deployment_guide = """# 巨潮年报爬虫工具 - 部署说明

## 📋 系统要求

### 最低要求
- **操作系统**: Windows 7/8/10/11 (推荐64位)
- **内存**: 4GB RAM (推荐8GB)
- **硬盘空间**: 500MB可用空间
- **网络**: 能够访问互联网

### 推荐配置
- **操作系统**: Windows 10/11 64位
- **内存**: 8GB RAM或更多
- **硬盘空间**: 2GB可用空间
- **网络**: 稳定的宽带连接

## 🚀 安装和运行

### 方法1: 直接运行（推荐）
1. 双击 `巨潮年报爬虫工具_优化版.exe`
2. 如果出现安全提示，选择"仍要运行"
3. 等待程序启动（首次可能需要30秒）

### 方法2: 使用启动脚本
1. 双击 `启动程序.bat`
2. 脚本会自动检查环境并启动程序
3. 如有问题会显示具体的错误信息

## ⚠️ 常见问题解决

### 1. 杀毒软件误报
**现象**: 杀毒软件提示病毒或恶意软件
**解决**: 
- 添加程序到杀毒软件白名单
- 或选择"信任此程序"/"仍要运行"

### 2. 缺少运行库
**现象**: 提示缺少 VCRUNTIME140.dll 或类似文件
**解决**: 
- 下载安装 Microsoft Visual C++ Redistributable
- 下载地址: https://aka.ms/vs/17/release/vc_redist.x64.exe

### 3. 网络连接问题
**现象**: 程序无法访问巨潮资讯网
**解决**: 
- 检查网络连接
- 关闭VPN或代理
- 允许程序通过防火墙

### 4. 权限不足
**现象**: 无法创建文件或文件夹
**解决**: 
- 右键程序，选择"以管理员身份运行"
- 或将程序放到有写入权限的目录

### 5. 程序启动慢
**现象**: 首次启动需要很长时间
**解决**: 
- 这是正常现象，程序在解压临时文件
- 后续启动会快很多
- 请耐心等待

## 📂 文件说明

- `巨潮年报爬虫工具_优化版.exe` - 主程序文件
- `启动程序.bat` - 环境检查和启动脚本
- `部署说明.md` - 本说明文件
- `使用说明_优化版.txt` - 程序使用说明

## 🔧 使用流程

1. **启动程序** - 双击exe文件或运行bat脚本
2. **填写参数** - 输入股票代码、搜索关键字等
3. **开始爬取** - 点击"开始爬取"按钮
4. **查看结果** - 程序会自动保存结果并可在窗口中查看

## 📞 技术支持

如果遇到问题：
1. 查看程序内的运行日志
2. 运行 `启动程序.bat` 查看环境检查结果
3. 确保网络连接正常
4. 尝试以管理员身份运行

## 📋 版本信息

- **版本**: 5.0 (布局优化版)
- **打包时间**: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}
- **兼容性**: Windows 7/8/10/11
- **架构**: x64

---
**注意**: 本程序仅用于学习和研究目的，请遵守相关法律法规和网站使用条款。
"""
    
    with open(f"{package_dir}/部署说明.md", "w", encoding="utf-8") as f:
        f.write(deployment_guide)
    
    # 复制使用说明
    if os.path.exists("使用说明_完整版.md"):
        shutil.copy2("使用说明_完整版.md", package_dir)
    
    # 创建版本信息文件
    version_info = f"""巨潮年报爬虫工具 - 版本信息

版本: 5.0 (布局优化版)
打包时间: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}
Python版本: 3.x
界面框架: CustomTkinter
主要特性:
- 现代化深色主题界面
- 左右分栏布局设计
- 自动滚动到日志区域
- 智能文件管理系统
- 内置数据表格查看器
- 历史记录自动保存

兼容性:
- Windows 7/8/10/11
- 64位系统推荐
- 无需安装Python环境
- 包含所有必要依赖
"""
    
    with open(f"{package_dir}/版本信息.txt", "w", encoding="utf-8") as f:
        f.write(version_info)
    
    print("✅ 创建启动脚本")
    print("✅ 创建部署说明")
    print("✅ 创建版本信息")
    
    # 创建压缩包（如果有7zip）
    try:
        import zipfile
        zip_path = f"deployment/{package_name}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, package_dir)
                    zipf.write(file_path, arc_path)
        print(f"✅ 创建压缩包: {zip_path}")
    except Exception as e:
        print(f"⚠️ 创建压缩包失败: {e}")
    
    print(f"\n🎉 部署包创建完成!")
    print(f"📁 位置: {package_dir}")
    print(f"📦 包含文件:")
    for file in os.listdir(package_dir):
        print(f"   - {file}")
    
    return package_dir

if __name__ == "__main__":
    create_deployment_package()
"""
