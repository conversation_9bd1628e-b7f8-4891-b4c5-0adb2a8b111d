# 巨潮年报爬虫工具使用说明 (完整版)

## 🎯 功能介绍
这是一个现代化的巨潮资讯网年报爬虫工具，具有以下特色：
- 🎨 现代化CustomTkinter深色主题界面
- 📊 实时数据展示和统计分析
- 💾 智能文件管理和历史记录保存
- 🔍 智能关键词搜索和统计
- 📈 可视化数据表格展示

## 🚀 主要功能

### 1. 数据爬取
- 从巨潮资讯网爬取上市公司年报PDF
- 自动转换PDF为文本格式
- 支持批量处理多个公司

### 2. 关键词统计
- 统计指定关键词在年报中的出现次数
- 生成公司级别和文件级别的统计报告
- 支持自定义关键词列表

### 3. 数据展示
- 内置数据表格查看器
- 支持数据导出为CSV格式
- 实时统计信息显示

### 4. 文件管理
- 智能文件夹组织
- 历史记录自动保存
- 支持单公司和批量查询的不同命名规则

## 📋 使用方法

### 启动程序
双击 `巨潮年报爬虫工具.exe` 启动程序

### 界面说明

#### 📈 公司股票代码
- 输入要爬取的股票代码，每行一个
- 支持深交所和沪交所股票
- 示例：
  ```
  300454
  300504
  300514
  ```

#### 🔍 搜索关键字
- 输入要搜索的报告类型
- 默认：`年度报告`
- 程序会自动排除摘要文件

#### 📅 时间范围
- 设置公告发布的时间范围
- 格式：YYYY-MM-DD
- 默认：2024-01-01 到 2025-12-31
- 💡 注意：2024年年报通常在2025年发布

#### 🔤 统计关键词
- 输入要统计的关键词，每行一个
- 程序会统计这些关键词在年报中的出现次数
- 默认包含常用的合作创新相关关键词

### 操作流程

1. **填写参数**：按照上述说明填写各项参数
2. **开始爬取**：点击"🚀 开始爬取"按钮
3. **查看进度**：观察进度条和运行日志
4. **查看结果**：任务完成后点击"📊 查看结果"按钮

### 按钮功能

- **🚀 开始爬取**：启动爬取任务
- **⏹ 停止**：中断正在运行的任务
- **📁 打开结果文件夹**：打开results文件夹
- **📚 历史记录**：打开历史记录文件夹
- **📊 查看结果**：在窗口中查看统计数据
- **🗑 清空日志**：清除运行日志

## 📂 文件组织结构

### 基础目录结构
```
results/
├── pdf/                    # 下载的PDF文件
├── txt/                    # 转换的文本文件
├── excel/                  # 最新的Excel统计文件
└── 历史记录/               # 历史结果保存
    ├── 300454_深信服_20250703_164818/    # 单公司结果
    │   ├── company_single_pdf.xlsx
    │   └── company_keywords.xlsx
    └── 批量查询_20250703_164819/         # 多公司结果
        ├── company_single_pdf.xlsx
        └── company_keywords.xlsx
```

### 文件命名规则

#### 单个公司查询
- 文件夹格式：`{公司代码}_{公司名称}_{时间戳}`
- 示例：`300454_深信服_20250703_164818`

#### 多个公司查询
- 文件夹格式：`批量查询_{时间戳}`
- 示例：`批量查询_20250703_164819`

#### 时间戳格式
- 格式：`YYYYMMDD_HHMMSS`
- 示例：`20250703_164818` (2025年7月3日 16:48:18)

## 📊 数据文件说明

### company_single_pdf.xlsx
- **内容**：每个PDF文件的关键词统计
- **用途**：查看具体文件的详细统计
- **格式**：文件名 + 各关键词出现次数

### company_keywords.xlsx
- **内容**：按公司汇总的关键词统计
- **用途**：公司级别的整体分析
- **格式**：公司代码 + 各关键词总计次数

## 🔧 高级功能

### 数据查看器
- 点击"📊 查看结果"打开内置数据查看器
- 支持表格形式查看统计结果
- 显示统计信息（文件数、关键词数、总出现次数）
- 支持导出为CSV格式

### 历史记录管理
- 每次运行都会保存完整的结果
- 按时间戳自动组织文件夹
- 支持单公司和批量查询的不同命名
- 点击"📚 历史记录"快速访问

### 文件导出
- 支持Excel格式（.xlsx和.xls）
- 支持CSV格式导出
- 自动格式化表格样式

## ⚠️ 注意事项

1. **网络连接**：确保网络连接正常
2. **运行时间**：大量数据爬取可能需要较长时间
3. **文件权限**：确保程序有写入文件的权限
4. **反爬虫**：程序已内置延时机制避免被反爬虫
5. **年报发布时间**：2024年年报通常在2025年3-4月发布

## 🐛 常见问题

### Q: 为什么没有找到2024年年报？
A: 2024年年报通常在2025年3-4月才发布，请调整时间范围或等待发布。

### Q: 程序运行很慢怎么办？
A: 这是正常现象，程序内置了延时机制防止被反爬虫，请耐心等待。

### Q: 如何查看历史结果？
A: 点击"📚 历史记录"按钮，或直接打开results/历史记录文件夹。

### Q: 数据表格显示不全怎么办？
A: 在数据查看器中可以滚动查看，或导出为CSV用Excel打开。

## 📞 技术支持

如有问题，请：
1. 查看运行日志中的错误信息
2. 检查网络连接和文件权限
3. 确认输入参数格式正确
4. 查看历史记录文件夹是否正常生成

---
**版本**: 4.0 (带数据展示和文件管理)  
**更新时间**: 2025年7月3日  
**开发**: 基于CustomTkinter的现代化界面
