"""
测试表格刷新功能
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
import pandas as pd
import os
import time

class TestTableWindow:
    """测试表格窗口"""
    
    def __init__(self):
        # 设置主题
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("表格刷新测试")
        self.root.geometry("1000x600+100+50")
        
        self.create_widgets()
        self.create_test_data()
        self.load_data()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ctk.CTkFrame(self.root, corner_radius=10)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="📊 表格刷新测试",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 按钮框架
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # 刷新按钮
        refresh_btn = ctk.CTkButton(
            button_frame,
            text="🔄 刷新数据",
            font=ctk.CTkFont(size=14),
            height=35,
            width=120,
            fg_color="#28a745",
            hover_color="#218838",
            command=self.refresh_data
        )
        refresh_btn.pack(side="left", padx=(0, 10))
        
        # 更新数据按钮
        update_btn = ctk.CTkButton(
            button_frame,
            text="📝 更新测试数据",
            font=ctk.CTkFont(size=14),
            height=35,
            width=140,
            fg_color="#fd7e14",
            hover_color="#e8681a",
            command=self.update_test_data
        )
        update_btn.pack(side="left", padx=(0, 10))
        
        # 创建表格框架
        table_frame = ctk.CTkFrame(main_frame, corner_radius=8)
        table_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # 创建表格
        self.create_table(table_frame)
        
    def create_table(self, parent):
        """创建表格"""
        # 创建表格容器
        table_container = tk.Frame(parent, bg="#212121")
        table_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建Treeview
        self.tree = ttk.Treeview(table_container, show="headings")
        
        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # 配置网格权重
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)
        
        # 设置表格样式
        style = ttk.Style()
        style.theme_use("clam")
        style.configure("Treeview", 
                       background="#2b2b2b",
                       foreground="white",
                       fieldbackground="#2b2b2b",
                       font=("Consolas", 11),
                       rowheight=25)
        style.configure("Treeview.Heading",
                       background="#1f538d",
                       foreground="white",
                       font=("Arial", 12, "bold"))
        
    def create_test_data(self):
        """创建测试数据"""
        os.makedirs("test_data", exist_ok=True)
        
        # 初始数据
        initial_data = {
            '文件名': [
                '300454_深信服_2024年年度报告',
                '300504_天邑股份_2024年年度报告'
            ],
            '协同创新': [10, 5],
            '合作开发': [15, 8],
            '战略合作': [20, 12]
        }
        
        df = pd.DataFrame(initial_data)
        df.to_excel("test_data/test_table.xlsx", index=False)
        print("✅ 创建初始测试数据")
        
    def update_test_data(self):
        """更新测试数据"""
        # 更新后的数据（更多列和行）
        updated_data = {
            '文件名': [
                '300454_深信服_2024年年度报告',
                '300504_天邑股份_2024年年度报告',
                '300514_友讯达_2024年年度报告'
            ],
            '协同创新': [15, 8, 12],
            '合资设立': [3, 5, 2],
            '合作开发': [22, 18, 15],
            '合作研发': [18, 12, 9],
            '共同开发': [7, 4, 6],
            '联合研发': [12, 9, 8],
            '战略合作': [25, 20, 18],
            '整合资源': [8, 6, 5]
        }
        
        df = pd.DataFrame(updated_data)
        df.to_excel("test_data/test_table.xlsx", index=False)
        print("✅ 更新测试数据")
        
        # 自动刷新显示
        self.refresh_data()
        
    def load_data(self):
        """加载数据"""
        try:
            file_path = "test_data/test_table.xlsx"
            if not os.path.exists(file_path):
                print("❌ 测试文件不存在")
                return
                
            # 读取Excel文件
            df = pd.read_excel(file_path, engine='openpyxl')
            
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 设置列
            columns = list(df.columns)
            self.tree["columns"] = columns
            
            # 重要：清除旧的列配置
            for col in self.tree["columns"]:
                self.tree.heading(col, text="")
                self.tree.column(col, width=0)
            
            # 设置列标题和宽度
            for col in columns:
                self.tree.heading(col, text=col, anchor="center")
                if col == columns[0]:  # 第一列（文件名）设置更宽
                    self.tree.column(col, width=350, anchor="w", minwidth=200)
                else:
                    self.tree.column(col, width=100, anchor="center", minwidth=60)
            
            # 确保表格显示列标题
            self.tree["show"] = "headings"
            
            # 插入数据
            for index, row in df.iterrows():
                values = [str(val) for val in row.values]
                self.tree.insert("", "end", values=values)
                
            print(f"✅ 加载数据: {len(df)} 行, {len(df.columns)} 列")
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            
    def refresh_data(self):
        """刷新数据"""
        print("🔄 刷新数据...")
        self.load_data()
        
    def run(self):
        """运行程序"""
        print("🧪 表格刷新测试程序启动")
        print("📋 测试步骤:")
        print("   1. 查看初始数据（2行3列）")
        print("   2. 点击'更新测试数据'按钮")
        print("   3. 观察表格是否正确显示新数据（3行8列）")
        print("   4. 点击'刷新数据'按钮测试刷新功能")
        print("   5. 检查列宽是否正确显示")
        
        self.root.mainloop()

if __name__ == "__main__":
    app = TestTableWindow()
    app.run()
