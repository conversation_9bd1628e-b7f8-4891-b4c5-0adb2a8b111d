"""
巨潮资讯网年报爬虫GUI程序 - CustomTkinter版本
"""
import customtkinter as ctk
from tkinter import messagebox, filedialog, ttk
from tkcalendar import DateEntry
import tkinter as tk
import threading
import os
import sys
import requests
import random
import time
import pdfplumber
import re
import xlwt
import pandas as pd
from datetime import datetime, timedelta


class DataDisplayWindow:
    """数据展示窗口"""
    def __init__(self, parent, excel_file_path):
        self.parent = parent
        self.excel_file_path = excel_file_path

        # 创建新窗口
        self.window = ctk.CTkToplevel(parent)
        self.window.title("📊 关键词统计结果")
        self.window.geometry("1200x700")
        self.window.minsize(1000, 600)

        # 设置窗口图标和属性
        self.window.transient(parent)
        self.window.grab_set()  # 模态窗口

        self.create_widgets()
        self.load_data()

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ctk.CTkFrame(self.window, corner_radius=10)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="📊 关键词统计结果",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 10))

        # 说明文字
        info_label = ctk.CTkLabel(
            main_frame,
            text="以下是各个PDF文件中关键词的统计结果",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        info_label.pack(pady=(0, 20))

        # 创建表格框架
        table_frame = ctk.CTkFrame(main_frame, corner_radius=8)
        table_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # 创建Treeview表格
        self.create_table(table_frame)

        # 按钮框架
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=(0, 20))

        # 导出按钮
        export_btn = ctk.CTkButton(
            button_frame,
            text="📤 导出为CSV",
            font=ctk.CTkFont(size=14),
            height=35,
            width=120,
            command=self.export_to_csv
        )
        export_btn.pack(side="left", padx=(0, 10))

        # 刷新按钮
        refresh_btn = ctk.CTkButton(
            button_frame,
            text="🔄 刷新数据",
            font=ctk.CTkFont(size=14),
            height=35,
            width=120,
            fg_color="#28a745",
            hover_color="#218838",
            command=self.load_data
        )
        refresh_btn.pack(side="left", padx=(0, 10))

        # 关闭按钮
        close_btn = ctk.CTkButton(
            button_frame,
            text="❌ 关闭",
            font=ctk.CTkFont(size=14),
            height=35,
            width=100,
            fg_color="#dc3545",
            hover_color="#c82333",
            command=self.window.destroy
        )
        close_btn.pack(side="right")

    def create_table(self, parent):
        """创建表格"""
        # 创建表格容器
        table_container = tk.Frame(parent, bg="#212121")
        table_container.pack(fill="both", expand=True, padx=10, pady=10)

        # 创建Treeview
        self.tree = ttk.Treeview(table_container, show="headings")

        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.tree.xview)

        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # 配置网格权重
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)

        # 设置表格样式
        style = ttk.Style()
        style.theme_use("clam")
        style.configure("Treeview",
                       background="#2b2b2b",
                       foreground="white",
                       fieldbackground="#2b2b2b",
                       font=("Consolas", 10))
        style.configure("Treeview.Heading",
                       background="#1f538d",
                       foreground="white",
                       font=("Arial", 11, "bold"))

    def load_data(self):
        """加载Excel数据"""
        try:
            if not os.path.exists(self.excel_file_path):
                messagebox.showerror("错误", f"文件不存在: {self.excel_file_path}")
                return

            # 读取Excel文件
            df = pd.read_excel(self.excel_file_path)

            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 设置列
            columns = list(df.columns)
            self.tree["columns"] = columns

            # 设置列标题和宽度
            for col in columns:
                self.tree.heading(col, text=col, anchor="center")
                if col == columns[0]:  # 第一列（文件名）设置更宽
                    self.tree.column(col, width=300, anchor="w")
                else:
                    self.tree.column(col, width=80, anchor="center")

            # 插入数据
            for index, row in df.iterrows():
                values = [str(val) for val in row.values]
                self.tree.insert("", "end", values=values)

            # 显示统计信息
            self.show_statistics(df)

        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {str(e)}")

    def show_statistics(self, df):
        """显示统计信息"""
        total_files = len(df)
        total_keywords = len(df.columns) - 1  # 减去文件名列

        # 计算总关键词数量
        numeric_cols = df.select_dtypes(include=['number']).columns
        total_count = df[numeric_cols].sum().sum()

        stats_text = f"📈 统计信息: {total_files} 个文件, {total_keywords} 个关键词, 总计 {total_count} 次出现"

        # 更新窗口标题
        self.window.title(f"📊 关键词统计结果 - {stats_text}")

    def export_to_csv(self):
        """导出为CSV文件"""
        try:
            # 选择保存位置
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="保存CSV文件"
            )

            if file_path:
                # 读取Excel并保存为CSV
                df = pd.read_excel(self.excel_file_path)
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                messagebox.showinfo("成功", f"数据已导出到: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")


class CninfoSpiderGUI:
    def __init__(self):
        # 设置CustomTkinter主题
        ctk.set_appearance_mode("system")  # 自动适应系统主题
        ctk.set_default_color_theme("blue")  # 可选: "blue", "green", "dark-blue"

        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("巨潮资讯网年报爬虫工具")
        self.root.geometry("1100x900")
        self.root.minsize(1000, 800)
        
        # 初始化变量
        self.is_running = False
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        # 创建主框架
        main_frame = ctk.CTkScrollableFrame(self.root, corner_radius=10)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="🏢 巨潮资讯网年报爬虫工具",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(0, 30))
        
        # 公司代码输入区域
        self.create_company_codes_section(main_frame)
        
        # 搜索关键字区域
        self.create_search_keyword_section(main_frame)
        
        # 时间范围区域
        self.create_time_range_section(main_frame)
        
        # 统计关键词区域
        self.create_keywords_section(main_frame)
        
        # 控制按钮区域
        self.create_control_buttons(main_frame)
        
        # 进度条和状态
        self.create_progress_section(main_frame)
        
        # 日志输出区域
        self.create_log_section(main_frame)
        
    def create_company_codes_section(self, parent):
        """创建公司代码输入区域"""
        # 标题
        codes_label = ctk.CTkLabel(
            parent,
            text="📈 公司股票代码",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        codes_label.pack(anchor="w", pady=(20, 8))

        # 说明文字
        codes_info = ctk.CTkLabel(
            parent,
            text="请输入要爬取的股票代码，每行一个",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        codes_info.pack(anchor="w", pady=(0, 12))

        # 文本输入框
        self.company_codes_text = ctk.CTkTextbox(
            parent,
            height=120,
            corner_radius=8,
            font=ctk.CTkFont(size=14)
        )
        self.company_codes_text.pack(fill="x", pady=(0, 25))
        self.company_codes_text.insert("1.0", "300454\n300504\n300514")
        
    def create_search_keyword_section(self, parent):
        """创建搜索关键字区域"""
        # 创建框架
        search_frame = ctk.CTkFrame(parent, corner_radius=10)
        search_frame.pack(fill="x", pady=(0, 20))
        
        # 标题
        search_label = ctk.CTkLabel(
            search_frame,
            text="🔍 搜索关键字",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        search_label.pack(anchor="w", padx=25, pady=(25, 8))

        # 输入框和说明的容器
        input_frame = ctk.CTkFrame(search_frame, fg_color="transparent")
        input_frame.pack(fill="x", padx=25, pady=(0, 25))

        # 输入框
        self.search_keyword = ctk.CTkEntry(
            input_frame,
            placeholder_text="输入搜索关键字，如：年度报告",
            font=ctk.CTkFont(size=14),
            height=40
        )
        self.search_keyword.pack(side="left", fill="x", expand=True, padx=(0, 15))
        self.search_keyword.insert(0, "年度报告")

        # 说明文字
        info_label = ctk.CTkLabel(
            input_frame,
            text="(自动排除摘要)",
            font=ctk.CTkFont(size=13),
            text_color="gray"
        )
        info_label.pack(side="right")
        
    def create_time_range_section(self, parent):
        """创建时间范围区域"""
        # 创建框架
        time_frame = ctk.CTkFrame(parent, corner_radius=10)
        time_frame.pack(fill="x", pady=(0, 25))

        # 标题
        time_label = ctk.CTkLabel(
            time_frame,
            text="📅 公告发布时间范围",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        time_label.pack(anchor="w", padx=25, pady=(25, 15))

        # 日期选择器容器
        date_frame = ctk.CTkFrame(time_frame, fg_color="transparent")
        date_frame.pack(fill="x", padx=25, pady=(0, 15))

        # 开始日期
        start_frame = ctk.CTkFrame(date_frame, fg_color="transparent")
        start_frame.pack(side="left", fill="x", expand=True, padx=(0, 20))

        ctk.CTkLabel(start_frame, text="开始日期:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", pady=(0, 8))

        # 使用Entry代替DateEntry来避免兼容性问题
        self.start_date_entry = ctk.CTkEntry(
            start_frame,
            placeholder_text="YYYY-MM-DD",
            font=ctk.CTkFont(size=14),
            height=35
        )
        self.start_date_entry.pack(fill="x")
        self.start_date_entry.insert(0, "2024-01-01")

        # 结束日期
        end_frame = ctk.CTkFrame(date_frame, fg_color="transparent")
        end_frame.pack(side="right", fill="x", expand=True, padx=(20, 0))

        ctk.CTkLabel(end_frame, text="结束日期:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", pady=(0, 8))

        self.end_date_entry = ctk.CTkEntry(
            end_frame,
            placeholder_text="YYYY-MM-DD",
            font=ctk.CTkFont(size=14),
            height=35
        )
        self.end_date_entry.pack(fill="x")
        self.end_date_entry.insert(0, "2025-12-31")

        # 说明文字
        info_label = ctk.CTkLabel(
            time_frame,
            text="💡 注意：2024年年报通常在2025年发布，请使用 YYYY-MM-DD 格式",
            font=ctk.CTkFont(size=13),
            text_color="#1f538d"
        )
        info_label.pack(anchor="w", padx=25, pady=(10, 25))
        
    def create_keywords_section(self, parent):
        """创建统计关键词区域"""
        # 标题
        keywords_label = ctk.CTkLabel(
            parent,
            text="🔤 统计关键词",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        keywords_label.pack(anchor="w", pady=(25, 8))

        # 说明文字
        keywords_info = ctk.CTkLabel(
            parent,
            text="请输入要统计的关键词，每行一个",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        keywords_info.pack(anchor="w", pady=(0, 12))

        # 文本输入框
        self.keywords_text = ctk.CTkTextbox(
            parent,
            height=180,
            corner_radius=8,
            font=ctk.CTkFont(size=14)
        )
        self.keywords_text.pack(fill="x", pady=(0, 25))
        
        default_keywords = """协同创新
合资设立
合作开发
合作研发
共同开发
联合研发
战略合作
整合资源"""
        self.keywords_text.insert("1.0", default_keywords)

    def create_control_buttons(self, parent):
        """创建控制按钮区域"""
        button_frame = ctk.CTkFrame(parent, fg_color="transparent")
        button_frame.pack(fill="x", pady=(20, 20))

        # 开始按钮
        self.start_button = ctk.CTkButton(
            button_frame,
            text="🚀 开始爬取",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=45,
            width=140,
            corner_radius=10,
            command=self.start_crawling
        )
        self.start_button.pack(side="left", padx=(0, 15))

        # 停止按钮
        self.stop_button = ctk.CTkButton(
            button_frame,
            text="⏹ 停止",
            font=ctk.CTkFont(size=16),
            height=45,
            width=100,
            corner_radius=10,
            fg_color="#dc3545",
            hover_color="#c82333",
            command=self.stop_crawling,
            state="disabled"
        )
        self.stop_button.pack(side="left", padx=(0, 15))

        # 打开文件夹按钮
        self.folder_button = ctk.CTkButton(
            button_frame,
            text="📁 打开结果文件夹",
            font=ctk.CTkFont(size=16),
            height=45,
            width=160,
            corner_radius=10,
            fg_color="#28a745",
            hover_color="#218838",
            command=self.open_result_folder
        )
        self.folder_button.pack(side="left", padx=(0, 15))

        # 查看结果按钮
        self.view_results_button = ctk.CTkButton(
            button_frame,
            text="📊 查看结果",
            font=ctk.CTkFont(size=16),
            height=45,
            width=120,
            corner_radius=10,
            fg_color="#17a2b8",
            hover_color="#138496",
            command=self.show_results,
            state="disabled"
        )
        self.view_results_button.pack(side="right", padx=(0, 15))

        # 清空日志按钮
        self.clear_log_button = ctk.CTkButton(
            button_frame,
            text="🗑 清空日志",
            font=ctk.CTkFont(size=16),
            height=45,
            width=120,
            corner_radius=10,
            fg_color="#6c757d",
            hover_color="#5a6268",
            command=self.clear_log
        )
        self.clear_log_button.pack(side="right")

    def create_progress_section(self, parent):
        """创建进度条和状态区域"""
        progress_frame = ctk.CTkFrame(parent, corner_radius=10)
        progress_frame.pack(fill="x", pady=(0, 20))

        # 进度条
        self.progress = ctk.CTkProgressBar(
            progress_frame,
            height=25,
            corner_radius=12
        )
        self.progress.pack(fill="x", padx=25, pady=(25, 15))
        self.progress.set(0)

        # 状态标签
        self.status_label = ctk.CTkLabel(
            progress_frame,
            text="🟢 就绪",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.status_label.pack(pady=(0, 25))

    def create_log_section(self, parent):
        """创建日志输出区域"""
        # 标题
        log_label = ctk.CTkLabel(
            parent,
            text="📋 运行日志",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        log_label.pack(anchor="w", pady=(25, 12))

        # 日志文本框
        self.log_text = ctk.CTkTextbox(
            parent,
            height=250,
            corner_radius=8,
            font=ctk.CTkFont(size=13, family="Consolas")
        )
        self.log_text.pack(fill="both", expand=True)

    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert("end", f"[{timestamp}] {message}\n")
        self.log_text.see("end")
        self.root.update()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete("1.0", "end")

    def update_status(self, status, emoji="🟢"):
        """更新状态显示"""
        self.status_label.configure(text=f"{emoji} {status}")

    def update_progress(self, value):
        """更新进度条"""
        self.progress.set(value)

    def start_crawling(self):
        """开始爬取"""
        if self.is_running:
            return

        # 验证输入
        company_codes = self.company_codes_text.get("1.0", "end").strip()
        if not company_codes:
            messagebox.showerror("错误", "请输入公司股票代码")
            return

        search_keyword = self.search_keyword.get().strip()
        if not search_keyword:
            messagebox.showerror("错误", "请输入搜索关键字")
            return

        keywords = self.keywords_text.get("1.0", "end").strip()
        if not keywords:
            messagebox.showerror("错误", "请输入统计关键词")
            return

        self.is_running = True
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        self.progress.start()
        self.update_status("运行中...", "🔵")

        # 清空日志
        self.log_text.delete("1.0", "end")
        self.log("🚀 开始爬取任务...")

        # 在新线程中运行爬取任务
        thread = threading.Thread(target=self.crawl_task)
        thread.daemon = True
        thread.start()

    def stop_crawling(self):
        """停止爬取"""
        self.is_running = False
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")
        self.progress.stop()
        self.progress.set(0)
        self.update_status("已停止", "🔴")
        self.log("⏹ 任务已停止")

        # 检查是否有结果文件，决定是否启用查看结果按钮
        excel_file_xlsx = os.path.join(os.getcwd(), "results", "excel", "company_single_pdf.xlsx")
        excel_file_xls = os.path.join(os.getcwd(), "results", "excel", "company_single_pdf.xls")
        if os.path.exists(excel_file_xlsx) or os.path.exists(excel_file_xls):
            self.view_results_button.configure(state="normal")
        else:
            self.view_results_button.configure(state="disabled")

    def open_result_folder(self):
        """打开结果文件夹"""
        result_folder = os.path.join(os.getcwd(), "results")
        if os.path.exists(result_folder):
            os.startfile(result_folder)
        else:
            messagebox.showinfo("提示", "结果文件夹不存在")

    def show_results(self):
        """显示统计结果"""
        # 优先使用新的xlsx格式
        excel_file_xlsx = os.path.join(os.getcwd(), "results", "excel", "company_single_pdf.xlsx")
        excel_file_xls = os.path.join(os.getcwd(), "results", "excel", "company_single_pdf.xls")

        excel_file = excel_file_xlsx if os.path.exists(excel_file_xlsx) else excel_file_xls

        if os.path.exists(excel_file):
            try:
                # 创建数据展示窗口
                DataDisplayWindow(self.root, excel_file)
            except Exception as e:
                messagebox.showerror("错误", f"打开结果窗口失败: {str(e)}")
        else:
            messagebox.showwarning("提示", "还没有生成统计结果，请先运行爬取任务")

    def crawl_task(self):
        """爬取任务主函数"""
        try:
            # 创建结果目录
            self.create_directories()

            # 获取参数
            company_codes = [code.strip() for code in self.company_codes_text.get("1.0", "end").strip().split('\n') if code.strip()]
            search_keyword = self.search_keyword.get().strip()
            start_date = self.start_date_entry.get().strip()
            end_date = self.end_date_entry.get().strip()

            # 验证日期格式
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                messagebox.showerror("错误", "日期格式不正确，请使用 YYYY-MM-DD 格式")
                return
            keywords = [kw.strip() for kw in self.keywords_text.get("1.0", "end").strip().split('\n') if kw.strip()]

            self.log(f"准备爬取 {len(company_codes)} 个公司的数据")
            self.log(f"搜索关键字: {search_keyword}")
            self.log(f"时间范围: {start_date} 到 {end_date}")
            self.log(f"统计关键词: {len(keywords)} 个")

            # 执行爬取流程
            self.execute_crawling(company_codes, search_keyword, start_date, end_date, keywords)

        except Exception as e:
            self.log(f"❌ 爬取过程中出现错误: {str(e)}")
            self.update_status("错误", "🔴")
        finally:
            if self.is_running:  # 只有在正常完成时才显示完成状态
                self.update_status("完成", "🟢")
            self.stop_crawling()

    def create_directories(self):
        """创建必要的目录"""
        dirs = ['results', 'results/pdf', 'results/txt', 'results/excel']
        for dir_name in dirs:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)

    def execute_crawling(self, company_codes, search_keyword, start_date, end_date, keywords):
        """执行爬取流程"""
        # 1. 爬取PDF
        self.update_progress(0.1)
        pdf_files = self.crawl_pdfs(company_codes, search_keyword, start_date, end_date)

        if not self.is_running:
            return

        # 2. 转换PDF为文本
        self.update_progress(0.5)
        txt_files = self.convert_pdfs_to_txt(pdf_files)

        if not self.is_running:
            return

        # 3. 统计关键词
        self.update_progress(0.8)
        self.analyze_keywords(txt_files, keywords)

        self.update_progress(1.0)
        self.log("✅ 所有任务完成！")

        # 启用查看结果按钮
        self.view_results_button.configure(state="normal")
        self.log("📊 点击'查看结果'按钮可以查看详细统计数据")

    def get_orgid_by_code(self, stock_code):
        """根据股票代码获取orgId"""
        try:
            orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            response = requests.get(orgid_url, headers=headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                stock_lists = data.get('stockList', [])
                for stock_info in stock_lists:
                    if stock_info.get('code') == stock_code:
                        return {
                            'code': stock_info['code'],
                            'orgId': stock_info['orgId'],
                            'zwjc': stock_info.get('zwjc', ''),
                        }
            return None
        except Exception as e:
            self.log(f"获取 {stock_code} orgId失败: {e}")
            return None

    def crawl_pdfs(self, company_codes, search_keyword, start_date, end_date):
        """爬取PDF文件"""
        self.log("开始爬取PDF文件...")
        pdf_files = []

        User_Agent = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]

        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'http://www.cninfo.com.cn',
            'X-Requested-With': 'XMLHttpRequest'
        }

        for i, stock_code in enumerate(company_codes, 1):
            if not self.is_running:
                break

            self.log(f"[{i}/{len(company_codes)}] 处理股票: {stock_code}")

            # 获取orgId
            stock_info = self.get_orgid_by_code(stock_code)
            if not stock_info:
                self.log(f"  跳过 {stock_code}: 无法获取orgId")
                continue

            orgId = stock_info['orgId']
            company_name = stock_info['zwjc']
            self.log(f"  公司: {company_name}")

            # 查询公告
            try:
                query_url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
                headers['User-Agent'] = random.choice(User_Agent)

                query_data = {
                    'pageNum': 1,
                    'pageSize': 30,
                    'tabName': 'fulltext',
                    'column': 'szse',
                    'stock': f'{stock_code},{orgId}',
                    'searchkey': '',
                    'secid': '',
                    'plate': 'sz',
                    'category': 'category_ndbg_szsh',
                    'trade': '',
                    'seDate': f'{start_date}~{end_date}',
                    'sortName': '',
                    'sortType': '',
                    'isHLtitle': 'true'
                }

                response = requests.post(query_url, headers=headers, data=query_data, timeout=10)
                result = response.json()
                announcements = result.get('announcements', [])

                self.log(f"  找到 {len(announcements)} 条公告")

                # 筛选和下载
                downloaded = 0
                for announcement in announcements:
                    if not self.is_running:
                        break

                    title = announcement.get('announcementTitle', '')

                    # 检查是否包含搜索关键字且不是摘要
                    if search_keyword in title and '摘要' not in title and '确认意见' not in title:
                        self.log(f"    准备下载: {title}")

                        # 下载PDF
                        adjunct_url = announcement.get('adjunctUrl', '')
                        if adjunct_url:
                            download_url = f'http://static.cninfo.com.cn/{adjunct_url}'
                            file_name = f"{stock_code}_{company_name}_{title}.pdf"
                            file_path = os.path.join('results/pdf', file_name)

                            if self.download_file(download_url, file_path):
                                pdf_files.append(file_path)
                                downloaded += 1
                                self.log(f"    ✅ 下载成功: {file_name}")
                            else:
                                self.log(f"    ❌ 下载失败: {file_name}")

                self.log(f"  {stock_code} 完成，下载了 {downloaded} 个文件")
                time.sleep(random.uniform(1, 3))  # 随机延时

            except Exception as e:
                self.log(f"  {stock_code} 查询失败: {e}")

        self.log(f"PDF爬取完成，共下载 {len(pdf_files)} 个文件")
        return pdf_files

    def download_file(self, url, file_path):
        """下载文件"""
        try:
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                return True
            return False
        except Exception as e:
            return False

    def convert_pdfs_to_txt(self, pdf_files):
        """将PDF转换为文本"""
        self.log("开始转换PDF为文本...")
        txt_files = []

        for i, pdf_path in enumerate(pdf_files, 1):
            if not self.is_running:
                break

            self.log(f"[{i}/{len(pdf_files)}] 转换: {os.path.basename(pdf_path)}")

            txt_path = pdf_path.replace('results/pdf/', 'results/txt/').replace('.pdf', '.txt')

            if self.extract_text_from_pdf(pdf_path, txt_path):
                txt_files.append(txt_path)
                self.log(f"  ✅ 转换成功")
            else:
                self.log(f"  ❌ 转换失败")

        self.log(f"PDF转换完成，共转换 {len(txt_files)} 个文件")
        return txt_files

    def extract_text_from_pdf(self, pdf_path, txt_path):
        """从PDF提取文本"""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"

            if text:
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                return True
            return False
        except Exception as e:
            return False

    def analyze_keywords(self, txt_files, keywords):
        """分析关键词"""
        self.log("开始关键词统计分析...")

        # 统计数据
        stat_all = {}  # 公司级别统计
        stat_sig = {}  # 单文件统计

        for i, txt_path in enumerate(txt_files, 1):
            if not self.is_running:
                break

            self.log(f"[{i}/{len(txt_files)}] 分析: {os.path.basename(txt_path)}")

            try:
                with open(txt_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 清理文本，只保留中文字符
                content = re.sub(r'[^\u4e00-\u9fa5]', '', content)

                # 统计关键词
                file_stat = {}
                for keyword in keywords:
                    count = content.count(keyword)
                    file_stat[keyword] = count

                # 获取文件信息
                file_prefix = os.path.basename(txt_path)[:-4]  # 去掉.txt
                company_id = file_prefix[:6]  # 前6位作为公司代码

                # 保存单文件统计
                stat_sig[file_prefix] = file_stat

                # 合并到公司统计
                if company_id in stat_all:
                    for keyword in keywords:
                        stat_all[company_id][keyword] += file_stat[keyword]
                else:
                    stat_all[company_id] = file_stat.copy()

                self.log(f"  ✅ 分析完成，找到关键词 {sum(file_stat.values())} 次")

            except Exception as e:
                self.log(f"  ❌ 分析失败: {e}")

        # 生成Excel文件
        self.generate_excel_reports(keywords, stat_all, stat_sig)

    def generate_excel_reports(self, keywords, stat_all, stat_sig):
        """生成Excel报告"""
        self.log("生成Excel报告...")

        try:
            # 创建带时间戳的文件夹
            result_folder = self.create_result_folder(stat_all)

            # 公司级别统计
            company_keywords_xlsx = os.path.join(result_folder, 'company_keywords.xlsx')
            company_keywords_xls = os.path.join(result_folder, 'company_keywords.xls')

            self.write_excel_pandas(company_keywords_xlsx, keywords, stat_all, "公司代码")
            self.write_excel(company_keywords_xls, keywords, stat_all)
            self.log(f"  ✅ 生成 {company_keywords_xlsx}")

            # 单文件统计 - 使用pandas格式以便更好地显示
            single_pdf_xlsx = os.path.join(result_folder, 'company_single_pdf.xlsx')
            single_pdf_xls = os.path.join(result_folder, 'company_single_pdf.xls')

            self.write_excel_pandas(single_pdf_xlsx, keywords, stat_sig, "文件名")
            self.write_excel(single_pdf_xls, keywords, stat_sig)
            self.log(f"  ✅ 生成 {single_pdf_xlsx}")

            # 同时在默认位置保存最新的文件（用于查看结果功能）
            self.write_excel_pandas('results/excel/company_keywords.xlsx', keywords, stat_all, "公司代码")
            self.write_excel_pandas('results/excel/company_single_pdf.xlsx', keywords, stat_sig, "文件名")
            self.write_excel('results/excel/company_keywords.xls', keywords, stat_all)
            self.write_excel('results/excel/company_single_pdf.xls', keywords, stat_sig)

            # 保存当前结果文件夹路径，用于后续查看
            self.current_result_folder = result_folder

        except Exception as e:
            self.log(f"  ❌ 生成Excel失败: {e}")

    def create_result_folder(self, stat_all):
        """创建结果文件夹"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 获取公司信息
        company_codes = list(stat_all.keys())

        if len(company_codes) == 1:
            # 单个公司：使用 公司代码_公司名称_时间戳 格式
            company_code = company_codes[0]

            # 尝试获取公司名称
            company_name = self.get_company_name_by_code(company_code)
            if company_name:
                folder_name = f"{company_code}_{company_name}_{timestamp}"
            else:
                folder_name = f"{company_code}_{timestamp}"
        else:
            # 多个公司：只使用时间戳
            folder_name = f"批量查询_{timestamp}"

        # 创建文件夹
        result_folder = os.path.join("results", "历史记录", folder_name)
        os.makedirs(result_folder, exist_ok=True)

        self.log(f"📁 创建结果文件夹: {result_folder}")
        return result_folder

    def get_company_name_by_code(self, company_code):
        """根据公司代码获取公司名称"""
        try:
            # 从已获取的orgId信息中查找公司名称
            stock_info = self.get_orgid_by_code(company_code)
            if stock_info:
                return stock_info.get('zwjc', '').replace('/', '_').replace('\\', '_')  # 替换文件名不允许的字符
            return None
        except:
            return None

    def write_excel_pandas(self, filename, keywords, data, index_name):
        """使用pandas写入Excel文件（更好的格式）"""
        try:
            # 创建DataFrame
            df_data = []
            for key, values in data.items():
                row = [key] + [values.get(keyword, 0) for keyword in keywords]
                df_data.append(row)

            columns = [index_name] + keywords
            df = pd.DataFrame(df_data, columns=columns)

            # 保存为Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='统计结果', index=False)

                # 获取工作表对象进行格式化
                worksheet = writer.sheets['统计结果']

                # 设置列宽
                worksheet.column_dimensions['A'].width = 40  # 文件名列更宽
                for i, keyword in enumerate(keywords, start=2):
                    col_letter = chr(64 + i)  # B, C, D...
                    worksheet.column_dimensions[col_letter].width = 12

        except Exception as e:
            self.log(f"  ❌ pandas写入失败: {e}")

    def write_excel(self, filename, keywords, data):
        """写入Excel文件（原有方法保持兼容性）"""
        try:
            workbook = xlwt.Workbook()
            worksheet = workbook.add_sheet('Sheet1')

            # 写入表头
            worksheet.write(0, 0, 'Company/File')
            for i, keyword in enumerate(keywords):
                worksheet.write(0, i + 1, keyword)

            # 写入数据
            row = 1
            for key, values in data.items():
                worksheet.write(row, 0, key)
                for i, keyword in enumerate(keywords):
                    worksheet.write(row, i + 1, values.get(keyword, 0))
                row += 1

            workbook.save(filename)
        except Exception as e:
            self.log(f"  ❌ xlwt写入失败: {e}")


def main():
    app = CninfoSpiderGUI()
    app.root.mainloop()


if __name__ == "__main__":
    main()
