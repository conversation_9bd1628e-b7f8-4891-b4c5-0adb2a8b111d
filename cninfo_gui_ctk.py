"""
巨潮资讯网年报爬虫GUI程序 - CustomTkinter版本
"""
import customtkinter as ctk
from tkinter import messagebox, filedialog
from tkcalendar import DateEntry
import tkinter as tk
import threading
import os
import sys
import requests
import random
import time
import pdfplumber
import re
import xlwt
from datetime import datetime, timedelta


class CninfoSpiderGUI:
    def __init__(self):
        # 设置CustomTkinter主题
        ctk.set_appearance_mode("dark")  # 可选: "light", "dark", "system"
        ctk.set_default_color_theme("blue")  # 可选: "blue", "green", "dark-blue"
        
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("巨潮资讯网年报爬虫工具")
        self.root.geometry("1000x850")
        self.root.minsize(900, 750)
        
        # 初始化变量
        self.is_running = False
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        # 创建主框架
        main_frame = ctk.CTkScrollableFrame(self.root, corner_radius=10)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame, 
            text="🏢 巨潮资讯网年报爬虫工具",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 30))
        
        # 公司代码输入区域
        self.create_company_codes_section(main_frame)
        
        # 搜索关键字区域
        self.create_search_keyword_section(main_frame)
        
        # 时间范围区域
        self.create_time_range_section(main_frame)
        
        # 统计关键词区域
        self.create_keywords_section(main_frame)
        
        # 控制按钮区域
        self.create_control_buttons(main_frame)
        
        # 进度条和状态
        self.create_progress_section(main_frame)
        
        # 日志输出区域
        self.create_log_section(main_frame)
        
    def create_company_codes_section(self, parent):
        """创建公司代码输入区域"""
        # 标题
        codes_label = ctk.CTkLabel(
            parent,
            text="📈 公司股票代码",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        codes_label.pack(anchor="w", pady=(20, 5))
        
        # 说明文字
        codes_info = ctk.CTkLabel(
            parent,
            text="请输入要爬取的股票代码，每行一个",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        codes_info.pack(anchor="w", pady=(0, 10))
        
        # 文本输入框
        self.company_codes_text = ctk.CTkTextbox(
            parent,
            height=100,
            corner_radius=8,
            font=ctk.CTkFont(size=12)
        )
        self.company_codes_text.pack(fill="x", pady=(0, 20))
        self.company_codes_text.insert("1.0", "300454\n300504\n300514")
        
    def create_search_keyword_section(self, parent):
        """创建搜索关键字区域"""
        # 创建框架
        search_frame = ctk.CTkFrame(parent, corner_radius=10)
        search_frame.pack(fill="x", pady=(0, 20))
        
        # 标题
        search_label = ctk.CTkLabel(
            search_frame,
            text="🔍 搜索关键字",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        search_label.pack(anchor="w", padx=20, pady=(20, 5))
        
        # 输入框和说明的容器
        input_frame = ctk.CTkFrame(search_frame, fg_color="transparent")
        input_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # 输入框
        self.search_keyword = ctk.CTkEntry(
            input_frame,
            placeholder_text="输入搜索关键字，如：年度报告",
            font=ctk.CTkFont(size=12),
            height=35
        )
        self.search_keyword.pack(side="left", fill="x", expand=True, padx=(0, 10))
        self.search_keyword.insert(0, "年度报告")
        
        # 说明文字
        info_label = ctk.CTkLabel(
            input_frame,
            text="(自动排除摘要)",
            font=ctk.CTkFont(size=11),
            text_color="gray"
        )
        info_label.pack(side="right")
        
    def create_time_range_section(self, parent):
        """创建时间范围区域"""
        # 创建框架
        time_frame = ctk.CTkFrame(parent, corner_radius=10)
        time_frame.pack(fill="x", pady=(0, 20))
        
        # 标题
        time_label = ctk.CTkLabel(
            time_frame,
            text="📅 公告发布时间范围",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        time_label.pack(anchor="w", padx=20, pady=(20, 10))
        
        # 日期选择器容器
        date_frame = ctk.CTkFrame(time_frame, fg_color="transparent")
        date_frame.pack(fill="x", padx=20, pady=(0, 10))
        
        # 开始日期
        start_frame = ctk.CTkFrame(date_frame, fg_color="transparent")
        start_frame.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        ctk.CTkLabel(start_frame, text="开始日期:", font=ctk.CTkFont(size=12)).pack(anchor="w")
        
        # 创建一个tkinter框架来放置DateEntry
        start_tk_frame = tk.Frame(start_frame, bg="#212121")
        start_tk_frame.pack(fill="x", pady=(5, 0))
        
        self.start_date = DateEntry(
            start_tk_frame,
            width=12,
            background='#1f538d',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy-mm-dd',
            font=('Arial', 10)
        )
        self.start_date.pack(fill="x")
        self.start_date.set_date(datetime(2024, 1, 1))
        
        # 结束日期
        end_frame = ctk.CTkFrame(date_frame, fg_color="transparent")
        end_frame.pack(side="right", fill="x", expand=True, padx=(10, 0))
        
        ctk.CTkLabel(end_frame, text="结束日期:", font=ctk.CTkFont(size=12)).pack(anchor="w")
        
        end_tk_frame = tk.Frame(end_frame, bg="#212121")
        end_tk_frame.pack(fill="x", pady=(5, 0))
        
        self.end_date = DateEntry(
            end_tk_frame,
            width=12,
            background='#1f538d',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy-mm-dd',
            font=('Arial', 10)
        )
        self.end_date.pack(fill="x")
        self.end_date.set_date(datetime(2025, 12, 31))
        
        # 说明文字
        info_label = ctk.CTkLabel(
            time_frame,
            text="💡 注意：2024年年报通常在2025年发布",
            font=ctk.CTkFont(size=11),
            text_color="#1f538d"
        )
        info_label.pack(anchor="w", padx=20, pady=(0, 20))
        
    def create_keywords_section(self, parent):
        """创建统计关键词区域"""
        # 标题
        keywords_label = ctk.CTkLabel(
            parent,
            text="🔤 统计关键词",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        keywords_label.pack(anchor="w", pady=(20, 5))
        
        # 说明文字
        keywords_info = ctk.CTkLabel(
            parent,
            text="请输入要统计的关键词，每行一个",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        keywords_info.pack(anchor="w", pady=(0, 10))
        
        # 文本输入框
        self.keywords_text = ctk.CTkTextbox(
            parent,
            height=150,
            corner_radius=8,
            font=ctk.CTkFont(size=12)
        )
        self.keywords_text.pack(fill="x", pady=(0, 20))
        
        default_keywords = """协同创新
合资设立
合作开发
合作研发
共同开发
联合研发
战略合作
整合资源"""
        self.keywords_text.insert("1.0", default_keywords)
