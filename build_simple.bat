@echo off
echo ========================================
echo CNInfo Crawler - Simple Build Script
echo ========================================

echo Cleaning old files...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo.
echo Building executable...
pyinstaller --onefile --windowed --name="CNInfo_Crawler" --hidden-import=customtkinter --hidden-import=tkinter --hidden-import=pdfplumber --hidden-import=requests --hidden-import=xlwt --hidden-import=pandas --hidden-import=openpyxl --hidden-import=PIL --hidden-import=darkdetect --collect-all=customtkinter --noupx --clean cninfo_gui_ctk.py

echo.
if exist "dist\CNInfo_Crawler.exe" (
    echo ========================================
    echo Build Successful!
    echo ========================================
    echo Executable: dist\CNInfo_Crawler.exe
    
    echo.
    echo Creating user manual...
    echo CNInfo Annual Report Crawler > "dist\README.txt"
    echo. >> "dist\README.txt"
    echo How to use: >> "dist\README.txt"
    echo 1. Double-click CNInfo_Crawler.exe to run >> "dist\README.txt"
    echo 2. Enter stock codes on the left side >> "dist\README.txt"
    echo 3. Set search keywords on the right side >> "dist\README.txt"
    echo 4. Set time range in YYYY-MM-DD format >> "dist\README.txt"
    echo 5. Enter statistical keywords >> "dist\README.txt"
    echo 6. Click Start Crawling button >> "dist\README.txt"
    echo 7. View results when completed >> "dist\README.txt"
    echo. >> "dist\README.txt"
    echo Results will be saved in results folder >> "dist\README.txt"
    echo Version: 5.0 Layout Optimized >> "dist\README.txt"
    
    echo Manual created: dist\README.txt
    
    echo.
    dir "dist\CNInfo_Crawler.exe" | find ".exe"
    
    echo.
    echo Build completed successfully!
    echo Open dist folder? (y/n)
    set /p choice=
    if /i "%choice%"=="y" start explorer "dist"
    
) else (
    echo ========================================
    echo Build Failed!
    echo ========================================
    echo Check error messages above
)

echo.
pause
