@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 巨潮年报爬虫工具 - 最终优化版打包
echo ========================================

echo 📋 布局优化特性:
echo    ✅ 公司代码和搜索关键字同行显示
echo    ✅ 窗口启动位置优化 (左上角)
echo    ✅ 自动滚动到日志区域
echo    ✅ 智能文件管理和历史记录
echo    ✅ 内置数据展示窗口

echo.
echo 🧹 清理旧的构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo.
echo 📦 开始打包...
pyinstaller ^
    --onefile ^
    --windowed ^
    --name="巨潮年报爬虫工具_优化版" ^
    --hidden-import=customtkinter ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=pdfplumber ^
    --hidden-import=requests ^
    --hidden-import=xlwt ^
    --hidden-import=pandas ^
    --hidden-import=openpyxl ^
    --hidden-import=PIL ^
    --hidden-import=darkdetect ^
    --collect-all=customtkinter ^
    cninfo_gui_ctk.py

echo.
if exist "dist\巨潮年报爬虫工具_优化版.exe" (
    echo ========================================
    echo ✅ 打包成功！
    echo ========================================
    echo 📁 可执行文件: dist\巨潮年报爬虫工具_优化版.exe
    
    echo.
    echo 📄 正在创建详细使用说明...
    
    (
    echo 巨潮年报爬虫工具 - 优化版使用说明
    echo.
    echo 🎯 主要特性:
    echo ✅ 现代化CustomTkinter深色主题界面
    echo ✅ 公司代码和搜索关键字同行布局
    echo ✅ 智能窗口定位 ^(左上角启动^)
    echo ✅ 自动滚动到日志区域
    echo ✅ 内置数据表格查看器
    echo ✅ 智能文件管理和历史记录
    echo.
    echo 🚀 使用方法:
    echo 1. 双击运行程序
    echo 2. 左侧输入股票代码 ^(每行一个^)
    echo 3. 右侧设置搜索关键字
    echo 4. 设置时间范围 ^(YYYY-MM-DD格式^)
    echo 5. 输入统计关键词 ^(每行一个^)
    echo 6. 点击"开始爬取"
    echo 7. 程序会自动滚动到日志区域显示进度
    echo 8. 完成后点击"查看结果"查看数据
    echo.
    echo 📂 文件组织:
    echo • results/excel/ - 最新结果文件
    echo • results/历史记录/ - 所有历史结果
    echo   - 单公司: 代码_公司名_时间戳/
    echo   - 多公司: 批量查询_时间戳/
    echo.
    echo 🔧 按钮功能:
    echo • 🚀 开始爬取 - 启动任务并滚动到日志
    echo • ⏹ 停止 - 中断任务
    echo • 📁 打开结果文件夹 - 查看results目录
    echo • 📚 历史记录 - 查看所有历史结果
    echo • 📊 查看结果 - 在窗口中展示数据表格
    echo • 🗑 清空日志 - 清除运行日志
    echo.
    echo 💡 注意事项:
    echo • 2024年年报通常在2025年3-4月发布
    echo • 程序内置延时防止反爬虫
    echo • 每次运行结果都会自动保存
    echo • 支持CSV格式数据导出
    echo.
    echo 版本: 5.0 ^(布局优化版^)
    echo 更新: 2025年7月3日
    ) > "dist\使用说明_优化版.txt"
    
    echo ✅ 使用说明已创建
    
    echo.
    echo 📊 文件信息:
    dir "dist\巨潮年报爬虫工具_优化版.exe" | find ".exe"
    
    echo.
    echo 🎉 打包完成！主要改进:
    echo    📐 左右分栏布局 ^(代码+搜索同行^)
    echo    📍 窗口位置优化 ^(左上角启动^)
    echo    🔽 自动滚动到日志区域
    echo    💾 智能文件管理系统
    echo    📊 内置数据查看器
    
    echo.
    set /p choice=是否打开结果文件夹？ (y/n): 
    if /i "%choice%"=="y" start explorer "dist"
    
) else (
    echo ========================================
    echo ❌ 打包失败！
    echo ========================================
    echo 请检查错误信息并重试
)

echo.
pause
