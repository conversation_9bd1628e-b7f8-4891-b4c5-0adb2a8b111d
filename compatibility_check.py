"""
兼容性检查脚本 - 检查程序在其他电脑上的运行环境
"""
import sys
import os
import platform
import subprocess

def check_system_info():
    """检查系统信息"""
    print("🖥️ 系统信息:")
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   架构: {platform.machine()}")
    print(f"   Python版本: {sys.version}")
    print(f"   当前目录: {os.getcwd()}")

def check_dependencies():
    """检查依赖库"""
    print("\n📦 检查依赖库:")
    
    required_packages = [
        'customtkinter',
        'tkinter', 
        'requests',
        'pandas',
        'pdfplumber',
        'xlwt',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - 缺失")
            missing_packages.append(package)
    
    return missing_packages

def check_network():
    """检查网络连接"""
    print("\n🌐 检查网络连接:")
    
    test_urls = [
        'www.cninfo.com.cn',
        'www.baidu.com'
    ]
    
    for url in test_urls:
        try:
            result = subprocess.run(['ping', '-n', '1', url], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"   ✅ {url} - 可访问")
            else:
                print(f"   ❌ {url} - 无法访问")
        except Exception as e:
            print(f"   ❌ {url} - 检查失败: {e}")

def check_file_permissions():
    """检查文件权限"""
    print("\n📁 检查文件权限:")
    
    test_dirs = ['results', 'temp_test']
    
    for dir_name in test_dirs:
        try:
            os.makedirs(dir_name, exist_ok=True)
            
            # 测试写入
            test_file = os.path.join(dir_name, 'test.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            
            # 测试读取
            with open(test_file, 'r') as f:
                content = f.read()
            
            # 清理
            os.remove(test_file)
            if dir_name == 'temp_test':
                os.rmdir(dir_name)
            
            print(f"   ✅ {dir_name} - 读写权限正常")
            
        except Exception as e:
            print(f"   ❌ {dir_name} - 权限不足: {e}")

def generate_compatibility_report():
    """生成兼容性报告"""
    print("\n" + "="*50)
    print("🔍 巨潮年报爬虫工具 - 兼容性检查报告")
    print("="*50)
    
    check_system_info()
    missing_packages = check_dependencies()
    check_network()
    check_file_permissions()
    
    print("\n📋 总结:")
    
    if not missing_packages:
        print("✅ 所有依赖库都已安装")
    else:
        print("❌ 缺少以下依赖库:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 解决方案:")
        print("   1. 使用打包后的exe文件（推荐）")
        print("   2. 或安装缺失的依赖库")
    
    print("\n🚀 运行建议:")
    if platform.system() == 'Windows':
        print("✅ Windows系统，兼容性良好")
        if missing_packages:
            print("💡 建议使用打包后的exe文件")
        else:
            print("✅ 可以直接运行Python脚本")
    else:
        print("⚠️ 非Windows系统，可能需要调整")
    
    print("\n📞 如果遇到问题:")
    print("   1. 确保使用Windows系统")
    print("   2. 使用打包后的exe文件")
    print("   3. 以管理员身份运行")
    print("   4. 检查网络连接")
    print("   5. 关闭杀毒软件或添加白名单")

if __name__ == "__main__":
    generate_compatibility_report()
"""
