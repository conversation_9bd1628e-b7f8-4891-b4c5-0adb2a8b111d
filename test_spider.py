"""
测试爬虫API响应
"""
import requests
import random
import json

User_Agent = [
    "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Media Center PC 6.0)",
    "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN) AppleWebKit/523.15 (KHTML, like Gecko, Safari/419.3) Arora/0.3 (Change: 287 c9dfb30)",
]

headers = {'Accept': 'application/json, text/javascript, */*; q=0.01',
           "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
           "Accept-Encoding": "gzip, deflate",
           "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-HK;q=0.6,zh-TW;q=0.5",
           'Host': 'www.cninfo.com.cn',
           'Origin': 'http://www.cninfo.com.cn',
           'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice',
           'X-Requested-With': 'XMLHttpRequest'
           }

def test_szse_annual(stock):
    """测试深交所年报查询"""
    query_path = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    headers['User-Agent'] = random.choice(User_Agent)
    query = {'pageNum': 1,
             'pageSize': 30,
             'tabName': 'fulltext',
             'column': 'szse',
             'stock': stock,
             'searchkey': '',
             'secid': '',
             'plate': 'sz',
             'category': 'category_ndbg_szsh;',
             'trade': '',
             'seDate': '2020-01-01+~+2024-12-31'
             }
    
    try:
        response = requests.post(query_path, headers=headers, data=query)
        print(f"深交所查询 {stock}:")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据键: {data.keys()}")
            
            if 'announcements' in data and data['announcements']:
                announcements = data['announcements']
                print(f"找到 {len(announcements)} 条公告")
                
                for i, announcement in enumerate(announcements[:3]):  # 只显示前3条
                    print(f"  {i+1}. {announcement.get('announcementTitle', 'N/A')}")
                    print(f"     发布时间: {announcement.get('announcementTime', 'N/A')}")
                    print(f"     文件URL: {announcement.get('adjunctUrl', 'N/A')}")
            else:
                print("没有找到公告数据")
                print(f"完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("-" * 50)

def test_sse_annual(stock):
    """测试沪交所年报查询"""
    query_path = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    headers['User-Agent'] = random.choice(User_Agent)
    query = {'pageNum': 1,
             'pageSize': 30,
             'tabName': 'fulltext',
             'column': 'sse',
             'stock': stock,
             'searchkey': '',
             'secid': '',
             'plate': 'sh',
             'category': 'category_ndbg_szsh;',
             'trade': '',
             'seDate': '2020-01-01+~+2024-12-31'
             }
    
    try:
        response = requests.post(query_path, headers=headers, data=query)
        print(f"沪交所查询 {stock}:")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据键: {data.keys()}")
            
            if 'announcements' in data and data['announcements']:
                announcements = data['announcements']
                print(f"找到 {len(announcements)} 条公告")
                
                for i, announcement in enumerate(announcements[:3]):  # 只显示前3条
                    print(f"  {i+1}. {announcement.get('announcementTitle', 'N/A')}")
                    print(f"     发布时间: {announcement.get('announcementTime', 'N/A')}")
                    print(f"     文件URL: {announcement.get('adjunctUrl', 'N/A')}")
            else:
                print("没有找到公告数据")
                print(f"完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("-" * 50)

if __name__ == "__main__":
    # 测试几个公司代码
    test_stocks = ['300454', '000001', '600000']  # 创业板、深市主板、沪市
    
    for stock in test_stocks:
        print(f"\n测试股票代码: {stock}")
        test_szse_annual(stock.strip())
        test_sse_annual(stock.strip())
